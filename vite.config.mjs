import {
	defineConfig
} from 'vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {
	ElementPlusResolver
} from 'unplugin-vue-components/resolvers'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { fileURLToPath } from 'url'

const __dirname = fileURLToPath(new URL('.', import.meta.url));

// https://vitejs.dev/config/
export default defineConfig({
	plugins: [
		AutoImport({
			resolvers: [ElementPlusResolver()],
		}),
		Components({
			resolvers: [ElementPlusResolver()],
		}),
		// // 使用插件移除重复的 @charset
		// require('postcss-remove-charset')({
		//     removeAll: true // 移除所有 charset 声明
		// }),
		// // 可选：添加排序插件确保规则顺序
		// require('postcss-sort-media-queries')(),
		vue()
	],
	resolve: {
		alias: {
			'@': path.resolve(__dirname, './src')
		}
	},
	define: {
		// 关闭生产环境 hydration 不匹配详细信息
		__VUE_PROD_HYDRATION_MISMATCH_DETAILS__: JSON.stringify(false),
		// 关闭 Options API 支持（可选）
		__VUE_OPTIONS_API__: JSON.stringify(false),
		// 关闭生产环境 devtools（可选）
		__VUE_PROD_DEVTOOLS__: JSON.stringify(false)
	},
	server: {
		proxy: {
			'/api': {
				target: 'http://127.0.0.1:8089',
				// target: 'http://*************:8089',
				changeOrigin: true,
				rewrite: (path) => path.replace(/^\/api/,''),
				// 如需处理WebSocket
				ws: true
			},
			'/auth': {
				target: 'http://localhost:8080',
				changeOrigin: true,
				secure: true,
				// 重写路径，保持 /auth 前缀
				rewrite: (path) => path
			}
		}
	}
})