import{Z as a,_ as e,a3 as t,aP as l,ac as s,ae as r,b as o,af as d,a5 as n,ag as i,aQ as c,aR as u,aS as v,am as p,o as g,k as f,m,w as y,l as b,v as h,F as k,t as w,n as _,x as A,aT as C,H as x,G as j,aU as z,aV as I,a6 as S,a7 as T,ao as $,E as N,z as U}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                             *//* empty css                    *//* empty css                      *//* empty css                    *//* empty css                  *//* empty css                 *//* empty css                *//* empty css                        */import{i as L}from"./index.9f8b8a79.js";const R=(a=0,e=10)=>L.get(`/task/getRunningTasks?pageSize=${a}&pageNum=${e}`),V=a=>L.get(`/task/stopTask?taskId=${a}`),P=a=>L.get(`/task/logsList?id=${a}`),B=a=>L.get(`/task/logsMsg?id=${a}`),D=(a,e)=>L.get(`/task/logsDetailList/${a}/${e}`),O=a=>L.get(`/task/downloadLogs/${a}`,{responseType:"blob"});const E={class:"task-list-container"},M={class:"card-header"},F=(a=>(S("data-v-f35b1f38"),a=a(),T(),a))((()=>b("span",null,"任务列表",-1))),G={key:0,class:"loading-content"},H={key:1,class:"empty-content"},J={class:"operation-buttons"},Q={class:"pagination-container"},Z={class:"task-msg"},q={class:"logs-container"},K={class:"logs-message-section"},W={class:"logs-message-content"},X={key:0,class:"loading-text"},Y={key:1,class:"logs-text"},aa={class:"refresh-btn-container"},ea={class:"logs-table-section"},ta={class:"operation-buttons"},la={class:"dialog-footer"},sa={__name:"List",setup(S){const T=a([]),L=a(!1),sa=a([]),ra=a(!1),oa=a({}),da=a(0),na=a(1),ia=a(10),ca=a(!1),ua=a(!1),va=a([]),pa=a(""),ga=a(""),fa=a(!1),ma=a(!1),ya=a([]),ba=a(null),ha=async()=>{var a;L.value=!0;try{const e=na.value-1,t=await R(e,ia.value);console.log("API响应数据:",t),200===t.code?t.data?t.data.data&&Array.isArray(t.data.data)?(T.value=t.data.data||[],da.value=t.data.total||0):t.data.list&&Array.isArray(t.data.list)?(T.value=t.data.list||[],da.value=t.data.total||0):t.data.records&&Array.isArray(t.data.records)?(T.value=t.data.records||[],da.value=t.data.total||0):Array.isArray(t.data)?(T.value=t.data,da.value=t.data.length):(T.value=[],da.value=0,console.warn("未能识别的数据格式:",t.data)):(T.value=[],da.value=0):$.error((null==(a=t.data)?void 0:a.message)||"获取任务列表失败")}catch(e){console.error("获取任务列表出错:",e),$.error("获取任务列表失败: "+(e.message||"未知错误"))}finally{L.value=!1}},ka=a=>{na.value=a,ha()},wa=a=>{ia.value=a,na.value=1,ha()},_a=()=>{ha()},Aa=a=>({0:"未开始",1:"执行中",2:"已完成",3:"已中止",4:"执行失败"}[a]||"未知状态"),Ca=a=>{if(!a||!a.msg)return 0;try{const e=a.msg.match(/总执行数据：(\d+)条/),t=a.msg.match(/已执行条数：(\d+)条/);if(e&&t){const a=parseInt(e[1]),l=parseInt(t[1]);if(a>0)return Math.floor(l/a*100)}return 0}catch(e){return 0}},xa=async()=>{ua.value=!0;try{const[a,e]=await Promise.all([P(ga.value),B(ga.value)]);console.log("日志列表响应:",a),console.log("日志消息响应:",e),200===a.code?Array.isArray(a.data)?va.value=a.data:a.data&&Array.isArray(a.data.data)?va.value=a.data.data:va.value=[]:(va.value=[],$.error("获取日志列表失败: "+(a.message||"未知错误"))),200===e.code?"string"==typeof e.data?pa.value=e.data:e.data&&"object"==typeof e.data?pa.value=e.data.message||e.data.msg||JSON.stringify(e.data):pa.value="暂无日志消息":(pa.value="获取日志消息失败",$.error("获取日志消息失败: "+(e.message||"未知错误")))}catch(a){console.error("获取日志数据出错:",a),$.error("获取日志数据失败: "+(a.message||"未知错误")),va.value=[],pa.value="获取日志数据失败"}finally{ua.value=!1}},ja=()=>{xa()},za=a=>{if(!a)return["暂无日志消息"];const e=a.split(/\r?\n|\r/).filter((a=>""!==a.trim()));return 0===e.length?["暂无日志消息"]:e};return e((()=>{ha()})),(a,e)=>{const S=t,R=l,P=s,B=r,Ia=U,Sa=o,Ta=d,$a=n,Na=i,Ua=c,La=u,Ra=v,Va=p;return g(),f("div",E,[m($a,null,{header:y((()=>[b("div",M,[F,m(S,{type:"primary",size:"small",onClick:_a},{default:y((()=>[h("刷新")])),_:1})])])),default:y((()=>[L.value?(g(),f("div",G,[m(R,{rows:5,animated:""})])):0===T.value.length?(g(),f("div",H," 暂无运行中的任务 ")):(g(),k(Ta,{key:2,data:T.value,border:"",style:{width:"100%"},height:"500","max-height":"500","header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",textAlign:"center"}},{default:y((()=>[m(P,{prop:"id",label:"任务ID",width:"180",align:"center"}),m(P,{prop:"fileName",label:"文件名称",align:"center"}),m(P,{prop:"shopNames",label:"店铺名称",align:"center"}),m(P,{prop:"dataNum",label:"数据总数",width:"100",align:"center"}),m(P,{prop:"taskStatus",label:"状态",width:"100",align:"center"},{default:y((a=>{return[m(B,{type:(e=a.row.taskStatus,{0:"info",1:"success",2:"success",3:"warning",4:"danger"}[e]||"info")},{default:y((()=>[h(w(Aa(a.row.taskStatus)),1)])),_:2},1032,["type"])];var e})),_:1}),m(P,{prop:"createTime",label:"创建时间",width:"160",align:"center"}),m(P,{label:"操作",width:"180",fixed:"right",align:"center"},{default:y((a=>[b("div",J,[m(Sa,{content:"查看日志",placement:"top","hide-after":1500},{default:y((()=>[m(S,{type:"primary",size:"small",onClick:e=>(async a=>{ga.value=a,ca.value=!0,await xa()})(a.row.id),circle:""},{default:y((()=>[m(Ia,null,{default:y((()=>[m(_(A))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024),m(Sa,{content:"停止任务",placement:"top","hide-after":1500},{default:y((()=>[m(S,{type:"danger",size:"small",onClick:e=>(async a=>{console.log("taskId",a);try{await N.confirm("确定要停止任务吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),sa.value.push(a);const e=await V(a);200===e.code?($.success("任务已成功停止"),ha()):$.error(e.data.message||"停止任务失败")}catch(e){"cancel"!==e&&(console.error("停止任务出错:",e),$.error("停止任务失败: "+(e.message||"未知错误")))}finally{sa.value=sa.value.filter((e=>e!==a))}})(a.row.id),loading:sa.value.includes(a.row.id),disabled:"1"!==a.row.taskStatus,circle:""},{default:y((()=>[m(Ia,null,{default:y((()=>[m(_(C))])),_:1})])),_:2},1032,["onClick","loading","disabled"])])),_:2},1024)])])),_:1})])),_:1},8,["data"]))])),_:1}),b("div",Q,[m(Na,{"current-page":na.value,"onUpdate:currentPage":e[0]||(e[0]=a=>na.value=a),"page-size":ia.value,"onUpdate:pageSize":e[1]||(e[1]=a=>ia.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:da.value,onSizeChange:wa,onCurrentChange:ka},null,8,["current-page","page-size","total"])]),m(Va,{modelValue:ra.value,"onUpdate:modelValue":e[2]||(e[2]=a=>ra.value=a),title:"任务详情",width:"500px"},{default:y((()=>[m(Ra,{column:1,border:""},{default:y((()=>[m(Ua,{label:"任务ID"},{default:y((()=>[h(w(oa.value.id),1)])),_:1}),m(Ua,{label:"文件名称"},{default:y((()=>[h(w(oa.value.fileName),1)])),_:1}),m(Ua,{label:"店铺名称"},{default:y((()=>[h(w(oa.value.shopNames),1)])),_:1}),m(Ua,{label:"数据总数"},{default:y((()=>[h(w(oa.value.dataNum),1)])),_:1}),m(Ua,{label:"创建时间"},{default:y((()=>[h(w(oa.value.createTime),1)])),_:1}),m(Ua,{label:"执行进度"},{default:y((()=>[m(La,{percentage:Ca(oa.value),status:"1"===oa.value.taskStatus?"success":"exception"},null,8,["percentage","status"])])),_:1}),m(Ua,{label:"执行消息"},{default:y((()=>[b("div",Z,w(oa.value.msg),1)])),_:1})])),_:1})])),_:1},8,["modelValue"]),m(Va,{modelValue:ca.value,"onUpdate:modelValue":e[4]||(e[4]=a=>ca.value=a),title:"任务日志",width:"900px","close-on-click-modal":!1},{footer:y((()=>[b("div",la,[m(S,{onClick:e[3]||(e[3]=a=>ca.value=!1)},{default:y((()=>[h("关闭")])),_:1})])])),default:y((()=>[b("div",q,[b("div",K,[b("div",W,[ua.value?(g(),f("div",X,"加载中...")):(g(),f("div",Y,[(g(!0),f(x,null,j(za(pa.value),((a,e)=>(g(),f("div",{key:e,class:"log-line"},w(a),1)))),128))]))]),b("div",aa,[m(S,{type:"primary",size:"small",onClick:ja,loading:ua.value,icon:"Refresh"},{default:y((()=>[h(" 刷新 ")])),_:1},8,["loading"])])]),b("div",ea,[m(Ta,{data:va.value,border:"",style:{width:"100%"},"header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",textAlign:"center"}},{default:y((()=>[m(P,{prop:"shopName",label:"店铺名称",align:"center"}),m(P,{prop:"progress",label:"进度",width:"100",align:"center"},{default:y((a=>[h(w(a.row.progress)+"% ",1)])),_:1}),m(P,{label:"创建时间/修改时间",width:"300",align:"center"},{default:y((a=>[h(w(a.row.createTime)+" / "+w(a.row.updateTime),1)])),_:1}),m(P,{label:"操作",width:"150",align:"center"},{default:y((a=>[b("div",ta,[m(Sa,{content:"查看详情",placement:"top","hide-after":1500},{default:y((()=>[m(S,{type:"primary",size:"small",onClick:e=>(async a=>{try{ba.value=a,ma.value=!0,fa.value=!0;const e=await D(a.taskId,a.shopId);console.log("详细日志响应:",e),200===e.code?e.data&&"object"==typeof e.data?Array.isArray(e.data.data)?ya.value=e.data.data:Array.isArray(e.data.list)?ya.value=e.data.list:Array.isArray(e.data.records)?ya.value=e.data.records:Array.isArray(e.data)?ya.value=e.data:(ya.value=[],console.warn("未能识别的详细日志数据格式:",e.data)):ya.value=[]:(ya.value=[],$.error("获取详细日志失败: "+(e.message||"未知错误")))}catch(e){console.error("获取详细日志出错:",e),$.error("获取详细日志失败: "+(e.message||"未知错误")),ya.value=[]}finally{ma.value=!1}})(a.row),circle:""},{default:y((()=>[m(Ia,null,{default:y((()=>[m(_(z))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024),m(Sa,{content:"下载日志",placement:"top","hide-after":1500},{default:y((()=>[m(S,{type:"success",size:"small",onClick:e=>(async a=>{try{const e=`${a.taskId}${a.shopId}.txt`,t=await O(e),l=new Blob([t.data]),s=window.URL.createObjectURL(l),r=document.createElement("a");r.href=s,r.download=e,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(s),$.success("文件下载成功")}catch(e){console.error("下载日志文件出错:",e),$.error("下载日志文件失败: "+(e.message||"未知错误"))}})(a.row),circle:""},{default:y((()=>[m(Ia,null,{default:y((()=>[m(_(I))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)])])),_:1})])),_:1},8,["data"])])])])),_:1},8,["modelValue"])])}},__scopeId:"data-v-f35b1f38"};export{sa as default};
