import{g as a,a8 as e,Z as s,u as l,_ as r,ak as o,a9 as t,aa as c,a3 as n,ab as i,o as p,k as d,m as u,w as m,l as f,t as g,n as h,v,av as w,a6 as b,a7 as y,aO as _,ao as V}from"./vendor.9a6f3141.js";/* empty css                     *//* empty css                 *//* empty css                   */import{s as x}from"./index.431f094f.js";const k={class:"login-container"},j={class:"header"},q=(a=>(b("data-v-f0d00b06"),a=a(),y(),a))((()=>f("h2",{class:"title"},"登录",-1))),C={class:"subtitle"},U={class:"captcha-wrapper"},D=["src"],I={__name:"Index",setup(b){var y;const I=null==(y=a())?void 0:y.appContext.config.globalProperties.$global,P=e({username:"",password:"",captcha:""}),R=e({username:[{required:!0,message:"用户名不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"}],captcha:[{required:!0,message:"验证码不能为空",trigger:"blur"}]}),$=s(null),z=s(""),F=s(!1),K=l(),L=()=>{z.value=`https://newadmin.buzhiyushu.cn/admin/generateCaptcha?t=${Date.now()}`},O=async()=>{$.value&&await $.value.validate((async a=>{if(a)try{F.value=!0;const a=new FormData;a.append("username",P.username),a.append("password",P.password),a.append("code",P.captcha),console.log('准备调用store.dispatch("login")',a),await x.dispatch("login",a),console.log('store.dispatch("login")调用成功');const e=K.currentRoute.value.query.redirect||"/welcome";console.log("跳转地址",e),_((()=>{window.location.href=e,K.replace(e),V.success("登录成功")}))}catch(e){console.error("登录错误详情:",e),L(),V.error(e.message||"登录失败")}finally{F.value=!1}else L()}))};return r((()=>{L()})),(a,e)=>{const s=o,l=t,r=c,b=n,y=i;return p(),d("div",k,[u(y,{ref_key:"formRef",ref:$,model:P,rules:R,onKeyup:w(O,["enter"])},{default:m((()=>{var a;return[f("div",j,[q,f("p",C,"欢迎使用"+g(null==(a=h(I))?void 0:a.system.name)+"管理系统",1),u(s)]),u(r,{prop:"username",class:"form-item"},{default:m((()=>[u(l,{modelValue:P.username,"onUpdate:modelValue":e[0]||(e[0]=a=>P.username=a),placeholder:"请输入用户名","prefix-icon":"User",class:"input-item"},null,8,["modelValue"])])),_:1}),u(r,{prop:"password",class:"form-item"},{default:m((()=>[u(l,{modelValue:P.password,"onUpdate:modelValue":e[1]||(e[1]=a=>P.password=a),type:"password",placeholder:"请输入密码","prefix-icon":"Lock","show-password":"",class:"input-item"},null,8,["modelValue"])])),_:1}),u(r,{prop:"captcha",class:"form-item"},{default:m((()=>[f("div",U,[u(l,{modelValue:P.captcha,"onUpdate:modelValue":e[2]||(e[2]=a=>P.captcha=a),placeholder:"请输入验证码","prefix-icon":"Picture",class:"captcha-input input-item"},null,8,["modelValue"]),f("img",{src:z.value,class:"captcha-image",onClick:L},null,8,D)])])),_:1}),u(s,{class:"custom-divider"}),u(b,{type:"primary",class:"login-btn",loading:F.value,onClick:O},{default:m((()=>[v("登录")])),_:1},8,["loading"])]})),_:1},8,["model","rules"])])}},__scopeId:"data-v-f0d00b06"};export{I as default};
