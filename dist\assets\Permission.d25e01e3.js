import{Z as e,a8 as s,_ as a,ao as o,ax as l,ay as i,o as r,k as n,l as t,m,w as p,v as d,t as u,az as c,F as f,an as y,a6 as h,a7 as V,a3 as v,ae as b,a5 as F,a9 as _,aa as g,ah as C,ai as k,aj as N,al as j,ab as P,am as w}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css                *//* empty css                      *//* empty css                   */import{g as U,u as x,a as I,d as z}from"./index.431f094f.js";const D={name:"PermissionManage",setup(){const l=e(!1),i=e([]),r=e(null),n=e(!1),t=e(!1),m=e(""),p=s({id:"",parentId:null,permissionName:"",permissionCode:null,description:"",type:1,path:"",component:"",icon:"",sort:0,status:1}),d={permissionName:[{required:!0,message:"请输入权限名称",trigger:"blur"}],permissionCode:[{required:!0,message:"请输入权限编码",trigger:"blur",validator:(e,s,a)=>{1===p.type||s?a():a(new Error("请输入权限编码"))}}],type:[{required:!0,message:"请选择权限类型",trigger:"change"}]},u=async()=>{l.value=!0;try{const e=await U();i.value=e.data||[]}catch(e){console.error("获取权限树失败",e),o.error("获取权限树失败")}finally{l.value=!1}};return a((()=>{u()})),{loading:l,permissionTree:i,permissionFormRef:r,permissionDialogVisible:n,isEdit:t,parentPermissionName:m,permissionForm:p,permissionRules:d,getTypeText:e=>{switch(e){case 1:return"目录";case 2:return"菜单";case 3:return"接口";default:return"未知"}},handleAddPermission:e=>{t.value=!1,p.id="",p.parentId=e?e.id:null,p.permissionName="",p.permissionCode=null,p.description="",p.type=1,p.path="",p.component="",p.icon="",p.sort=0,p.status=1,m.value=e?e.name:"无（根权限）",n.value=!0},handleEditPermission:e=>{if(t.value=!0,p.id=e.id,p.parentId=e.parentId,p.permissionName=e.name,p.permissionCode=e.code,p.description=e.description,p.type=e.type,p.path=e.path||"",p.component=e.component||"",p.icon=e.icon||"",p.sort=e.sort,p.status=e.status,e.parentId){const s=(e,a)=>{for(const o of e){if(o.id===a)return o.name;if(o.children&&o.children.length>0){const e=s(o.children,a);if(e)return e}}return null};m.value=s(i.value,e.parentId)||"未知"}else m.value="无（根权限）";n.value=!0},submitPermissionForm:async()=>{r.value&&await r.value.validate((async e=>{if(e)try{1===p.type&&(p.permissionCode=null);const e={id:p.id,parentId:p.parentId,permissionName:p.permissionName,permissionCode:p.permissionCode,description:p.description,type:p.type,path:p.path,component:p.component,icon:p.icon,sort:p.sort,status:p.status};t.value?(await x(e),o.success("更新成功")):(await I(e),o.success("添加成功")),n.value=!1,u()}catch(s){console.error(t.value?"更新权限失败":"添加权限失败",s),o.error(t.value?"更新权限失败":"添加权限失败")}}))},handleDeletePermission:async e=>{try{await z(e),o.success("删除成功"),u()}catch(s){console.error("删除权限失败",s),o.error("删除权限失败")}}}}},T={class:"permission-manage"},E={class:"permission-tree"},R={class:"header"},q=(e=>(h("data-v-1b5f160b"),e=e(),V(),e))((()=>t("span",null,"权限管理",-1))),A={class:"custom-tree-node"},M={class:"dialog-footer"};D.render=function(e,s,a,o,h,V){const U=v,x=b,I=l,z=i,D=F,Z=_,B=g,G=C,H=k,J=N,K=j,L=P,O=w;return r(),n("div",T,[t("div",E,[m(D,null,{header:p((()=>[t("div",R,[q,m(U,{type:"primary",onClick:s[0]||(s[0]=e=>o.handleAddPermission(null))},{default:p((()=>[d("新增权限")])),_:1})])])),default:p((()=>[m(z,{data:o.permissionTree,props:{label:"name"},"node-key":"id","default-expand-all":""},{default:p((({node:e,data:a})=>[t("div",A,[t("div",null,[t("span",null,u(a.name),1),m(x,{size:"small",class:"ml-10"},{default:p((()=>[d(u(a.code),1)])),_:2},1024),m(x,{size:"small",type:"success",class:"ml-10"},{default:p((()=>[d(u(o.getTypeText(a.type)),1)])),_:2},1024)]),t("div",null,[m(U,{type:"primary",size:"small",onClick:c((e=>o.handleAddPermission(a)),["stop"])},{default:p((()=>[d(" 添加子权限 ")])),_:2},1032,["onClick"]),m(U,{type:"warning",size:"small",onClick:c((e=>o.handleEditPermission(a)),["stop"])},{default:p((()=>[d(" 编辑 ")])),_:2},1032,["onClick"]),m(I,{title:"确认删除?",onConfirm:e=>o.handleDeletePermission(a.id)},{reference:p((()=>[m(U,{type:"danger",size:"small",onClick:s[1]||(s[1]=c((()=>{}),["stop"]))},{default:p((()=>[d("删除")])),_:1})])),_:2},1032,["onConfirm"])])])])),_:1},8,["data"])])),_:1})]),m(O,{modelValue:o.permissionDialogVisible,"onUpdate:modelValue":s[14]||(s[14]=e=>o.permissionDialogVisible=e),title:o.isEdit?"编辑权限":"新增权限"},{footer:p((()=>[t("span",M,[m(U,{onClick:s[13]||(s[13]=e=>o.permissionDialogVisible=!1)},{default:p((()=>[d("取消")])),_:1}),m(U,{type:"primary",onClick:o.submitPermissionForm},{default:p((()=>[d("确定")])),_:1},8,["onClick"])])])),default:p((()=>[m(L,{model:o.permissionForm,"label-width":"100px",rules:o.permissionRules,ref:"permissionFormRef"},{default:p((()=>[m(B,{label:"上级权限"},{default:p((()=>[m(Z,{modelValue:o.parentPermissionName,"onUpdate:modelValue":s[2]||(s[2]=e=>o.parentPermissionName=e),disabled:""},null,8,["modelValue"])])),_:1}),1!==o.permissionForm.type?(r(),f(B,{key:0,label:"权限名称",prop:"permissionName"},{default:p((()=>[m(Z,{modelValue:o.permissionForm.permissionName,"onUpdate:modelValue":s[3]||(s[3]=e=>o.permissionForm.permissionName=e),placeholder:"请输入权限名称"},null,8,["modelValue"])])),_:1})):y("",!0),1!==o.permissionForm.type?(r(),f(B,{key:1,label:"权限编码",prop:"permissionCode"},{default:p((()=>[m(Z,{modelValue:o.permissionForm.permissionCode,"onUpdate:modelValue":s[4]||(s[4]=e=>o.permissionForm.permissionCode=e),placeholder:"请输入权限编码"},null,8,["modelValue"])])),_:1})):y("",!0),1===o.permissionForm.type?(r(),f(B,{key:2,label:"目录名称",prop:"permissionName"},{default:p((()=>[m(Z,{modelValue:o.permissionForm.permissionName,"onUpdate:modelValue":s[5]||(s[5]=e=>o.permissionForm.permissionName=e),placeholder:"请输入目录名称"},null,8,["modelValue"])])),_:1})):y("",!0),m(B,{label:"权限类型",prop:"type"},{default:p((()=>[m(H,{modelValue:o.permissionForm.type,"onUpdate:modelValue":s[6]||(s[6]=e=>o.permissionForm.type=e),placeholder:"请选择权限类型",style:{width:"100%"}},{default:p((()=>[m(G,{value:1,label:"目录"}),m(G,{value:2,label:"菜单"}),m(G,{value:3,label:"接口"})])),_:1},8,["modelValue"])])),_:1}),1===o.permissionForm.type||2===o.permissionForm.type?(r(),f(B,{key:3,label:"路由路径",prop:"path"},{default:p((()=>[m(Z,{modelValue:o.permissionForm.path,"onUpdate:modelValue":s[7]||(s[7]=e=>o.permissionForm.path=e),placeholder:"请输入路由路径"},null,8,["modelValue"])])),_:1})):y("",!0),1===o.permissionForm.type||2===o.permissionForm.type?(r(),f(B,{key:4,label:"组件路径",prop:"component"},{default:p((()=>[m(Z,{modelValue:o.permissionForm.component,"onUpdate:modelValue":s[8]||(s[8]=e=>o.permissionForm.component=e),placeholder:"请输入组件路径"},null,8,["modelValue"])])),_:1})):y("",!0),1===o.permissionForm.type||2===o.permissionForm.type?(r(),f(B,{key:5,label:"图标",prop:"icon"},{default:p((()=>[m(Z,{modelValue:o.permissionForm.icon,"onUpdate:modelValue":s[9]||(s[9]=e=>o.permissionForm.icon=e),placeholder:"请输入图标"},null,8,["modelValue"])])),_:1})):y("",!0),m(B,{label:"排序",prop:"sort"},{default:p((()=>[m(J,{modelValue:o.permissionForm.sort,"onUpdate:modelValue":s[10]||(s[10]=e=>o.permissionForm.sort=e),min:0},null,8,["modelValue"])])),_:1}),m(B,{label:"权限描述",prop:"description"},{default:p((()=>[m(Z,{modelValue:o.permissionForm.description,"onUpdate:modelValue":s[11]||(s[11]=e=>o.permissionForm.description=e),placeholder:"请输入权限描述",type:"textarea"},null,8,["modelValue"])])),_:1}),m(B,{label:"状态"},{default:p((()=>[m(K,{modelValue:o.permissionForm.status,"onUpdate:modelValue":s[12]||(s[12]=e=>o.permissionForm.status=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])},D.__scopeId="data-v-1b5f160b";export{D as default};
