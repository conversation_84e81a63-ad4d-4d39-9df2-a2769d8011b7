import{T as e,Z as a,_ as l,a9 as s,aa as r,a3 as o,ab as d,o as u,k as n,l as i,m as t,w as c,n as p,r as m,aX as v,aY as g,q as f,F as h,aZ as w,an as y,v as b,a6 as k,a7 as T,ao as V}from"./vendor.9a6f3141.js";/* empty css                     *//* empty css                 */const x=e=>(k("data-v-049d561a"),e=e(),T(),e),I={class:"register-page"},_={class:"register-container"},N={class:"register-form"},C=x((()=>i("div",{class:"form-header"},[i("h2",null,"与书有行"),i("div",{class:"language-switch"},[i("span",null,"A")])],-1))),M={class:"captcha-row"},z=["src"],U=x((()=>i("div",{class:"login-link"},[i("span",null,"使用已有账户登录")],-1))),j={__name:"index",setup(k){const T=e(),x=a(!1),j=a(),q=a(""),S=a(!0),P=a({pddMallId:"",pddMallName:"",type:"",accessToken:"",skuSpec:""}),E=a({username:"",phoneNumber:"",password:"",confirmPassword:"",inviteCode:"",code:"",uuid:"",clientId:"e5cd7e4891bf95d1d19206ce24a7b32e",grantType:"password",tenantId:"000000",userType:"sys_user"}),R={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:2,max:20,message:"用户名长度在2到20个字符",trigger:"blur"}],phoneNumber:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:5,max:20,message:"密码长度在5到20个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(e,a,l)=>{a!==E.value.password?l(new Error("两次输入密码不一致")):l()},trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur",validator:(e,a,l)=>{S.value?a?l():l(new Error("请输入验证码")):l()}}]},O=async()=>{try{const e=await fetch("/auth/code",{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok){const a=await e.json();if(200===a.code){const{data:e}=a;S.value=void 0===e.captchaEnabled||e.captchaEnabled,S.value?(E.value.uuid=e.uuid,q.value="data:image/gif;base64,"+e.img):(E.value.uuid="",q.value="",console.log("验证码功能未启用"))}else V.error(a.msg||"获取验证码失败")}else V.error("获取验证码失败，请重试")}catch(e){console.error("获取验证码失败:",e),V.error("网络错误，获取验证码失败")}},Z=async()=>{try{if(!(await j.value.validate()))return;x.value=!0;const a={username:E.value.username,password:E.value.password,phoneNumber:E.value.phoneNumber,inviteCode:E.value.inviteCode,clientId:E.value.clientId,grantType:E.value.grantType,tenantId:E.value.tenantId,userType:E.value.userType,pddMallId:P.value.pddMallId,pddMallName:P.value.pddMallName,pddType:P.value.type,accessToken:P.value.accessToken,skuSpec:P.value.skuSpec};S.value&&(a.code=E.value.code,a.uuid=E.value.uuid),console.log("注册数据:",a);const l=await fetch("/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),s=await l.json();if(200===s.code){const a="https://erp.buzhiyushu.cn/";try{await navigator.clipboard.writeText(a),V.success("注册成功！登录链接https://erp.buzhiyushu.cn/已复制到剪贴板")}catch(e){V.success(`注册成功！请复制链接：${a}`)}}else V.error(s.msg||"注册失败，请重试"),S.value&&s.msg&&(s.msg.includes("验证码")||s.msg.includes("captcha"))&&(O(),E.value.code="")}catch(a){console.error("注册失败:",a),V.error("注册失败，请重试")}finally{x.value=!1}};return l((()=>{(()=>{const e=T.query;P.value={pddMallId:e.pddMallId||"",pddMallName:decodeURIComponent(e.pddMallName||""),type:e.type||"",accessToken:e.accessToken||"",skuSpec:decodeURIComponent(e.skuSpec||"")},P.value.pddMallId&&(E.value.username="pdd"+P.value.pddMallId),console.log("获取到的URL参数:",P.value)})(),O()})),(e,a)=>{const l=s,k=r,T=o,V=d;return u(),n("div",I,[i("div",_,[i("div",N,[C,t(V,{ref_key:"formRef",ref:j,model:E.value,rules:R,"label-width":"0"},{default:c((()=>[t(k,{prop:"username"},{default:c((()=>[t(l,{modelValue:E.value.username,"onUpdate:modelValue":a[0]||(a[0]=e=>E.value.username=e),placeholder:"用户名",size:"large","prefix-icon":p(m)},null,8,["modelValue","prefix-icon"])])),_:1}),t(k,{prop:"phoneNumber"},{default:c((()=>[t(l,{modelValue:E.value.phoneNumber,"onUpdate:modelValue":a[1]||(a[1]=e=>E.value.phoneNumber=e),placeholder:"手机号",size:"large","prefix-icon":p(v)},null,8,["modelValue","prefix-icon"])])),_:1}),t(k,{prop:"password"},{default:c((()=>[t(l,{modelValue:E.value.password,"onUpdate:modelValue":a[2]||(a[2]=e=>E.value.password=e),type:"password",placeholder:"密码",size:"large","prefix-icon":p(g),"show-password":""},null,8,["modelValue","prefix-icon"])])),_:1}),t(k,{prop:"confirmPassword"},{default:c((()=>[t(l,{modelValue:E.value.confirmPassword,"onUpdate:modelValue":a[3]||(a[3]=e=>E.value.confirmPassword=e),type:"password",placeholder:"确认密码",size:"large","prefix-icon":p(g),"show-password":""},null,8,["modelValue","prefix-icon"])])),_:1}),t(k,{prop:"inviteCode"},{default:c((()=>[t(l,{modelValue:E.value.inviteCode,"onUpdate:modelValue":a[4]||(a[4]=e=>E.value.inviteCode=e),placeholder:"邀请码",size:"large","prefix-icon":p(f)},null,8,["modelValue","prefix-icon"])])),_:1}),S.value?(u(),h(k,{key:0,prop:"code"},{default:c((()=>[i("div",M,[t(l,{modelValue:E.value.code,"onUpdate:modelValue":a[5]||(a[5]=e=>E.value.code=e),placeholder:"验证码",size:"large","prefix-icon":p(w)},null,8,["modelValue","prefix-icon"]),i("div",{class:"captcha-image",onClick:O},[q.value?(u(),n("img",{key:0,src:q.value,alt:"验证码"},null,8,z)):y("",!0)])])])),_:1})):y("",!0),t(k,null,{default:c((()=>[t(T,{type:"primary",size:"large",class:"register-btn",onClick:Z,loading:x.value},{default:c((()=>[b(" 注册 ")])),_:1},8,["loading"])])),_:1}),U])),_:1},8,["model"])])])])}},__scopeId:"data-v-049d561a"};export{j as default};
