var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,n=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,s=(e,a)=>{for(var l in a||(a={}))r.call(a,l)&&n(e,l,a[l]);if(t)for(var l of t(a))o.call(a,l)&&n(e,l,a[l]);return e},i=(e,t)=>a(e,l(t));import{Z as u,a8 as d,_ as c,ao as p,a9 as m,aa as f,a3 as g,ab as v,ac as b,ae as y,af as _,ag as w,aQ as h,aS as S,am as j,as as z,o as k,k as C,l as N,m as O,w as T,v as V,at as x,F as P,t as U,E as A}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                   *//* empty css                             *//* empty css                      *//* empty css                    *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                     */import{i as B}from"./index.431f094f.js";import{A as E}from"./ActionBar.acef8039.js";import"./RefreshButton.c2d64dc7.js";const I=e=>{const a=i(s({},e),{pageNum:e.page,pageSize:e.size});return delete a.page,delete a.size,B.get("/vas/pageQuery",{params:a})},R=e=>B.delete(`/vas/${e}`);const D={class:"list-container"},Q={class:"search-area"},$={class:"search-area"},F={class:"pagination-container"},L={class:"dialog-footer"},Z={__name:"List",setup(e){const a=u([]),l=u(!1),t=u(null),r=d({orderSn:"",mallName:"",payStatus:"",refundStatus:""}),o=d({current:1,size:10,total:0}),n=u([]),B=u(!0),Z=u(!0),q=u({}),G=u(!1),H=u("服务订单详情"),J=e=>{n.value=e.map((e=>e.id)),B.value=1!=e.length,Z.value=!e.length};c((()=>{K()}));const K=async()=>{l.value=!0;try{const e=i(s({},r),{page:o.current,size:o.size,mallName:null!==r.mallName?r.mallName:void 0}),t=await I(e);200===t.code?(a.value=t.data.list||[],o.total=t.data.total||0):p.error(t.message||"获取数据失败")}catch(e){console.error("获取数据失败:",e),p.error("获取数据失败，请稍后重试"),a.value=[],o.total=0}finally{l.value=!1}},M=()=>{K()},W=()=>{o.current=1,K()},X=()=>{Object.keys(r).forEach((e=>{r[e]=""})),o.current=1,K()},Y=e=>{o.size=e,o.current=1,K()},ee=e=>{o.current=e,K()};return(e,s)=>{const i=m,u=f,d=g,c=v,B=b,I=y,Z=_,ae=w,le=h,te=S,re=j,oe=z;return k(),C("div",D,[N("div",Q,[O(c,{inline:!0,model:r},{default:T((()=>[O(u,{label:"订单号"},{default:T((()=>[O(i,{modelValue:r.orderSn,"onUpdate:modelValue":s[0]||(s[0]=e=>r.orderSn=e),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])])),_:1}),O(u,{label:"店铺名称"},{default:T((()=>[O(i,{modelValue:r.mallName,"onUpdate:modelValue":s[1]||(s[1]=e=>r.mallName=e),placeholder:"请输入店铺名称",clearable:""},null,8,["modelValue"])])),_:1}),O(u,null,{default:T((()=>[O(d,{type:"primary",onClick:W},{default:T((()=>[V("搜索")])),_:1}),O(d,{onClick:X},{default:T((()=>[V("重置")])),_:1})])),_:1})])),_:1},8,["model"])]),N("div",$,[O(E,{onRefresh:M},{left:T((()=>[])),_:1}),x((k(),P(Z,{ref_key:"tableRef",ref:t,data:a.value,border:"",stripe:"",style:{width:"100%"},onSelectionChange:J},{default:T((()=>[O(B,{type:"selection",width:"55",align:"center"}),O(B,{label:"ID",align:"center",prop:"id",width:"80"}),O(B,{label:"订单号",align:"center",prop:"orderSn",width:"180"}),O(B,{label:"店铺名称",align:"center",prop:"mallName"}),O(B,{label:"服务名称",align:"center",prop:"serviceName"}),O(B,{label:"实付价格",align:"center",prop:"amount"},{default:T((a=>[V(U(e.formatAmount(a.row.amount)),1)])),_:1}),O(B,{label:"支付状态",align:"center",prop:"payStatus",width:"100"},{default:T((e=>[O(I,{type:1===e.row.payStatus?"success":"danger"},{default:T((()=>[V(U(1===e.row.payStatus?"已支付":"未支付"),1)])),_:2},1032,["type"])])),_:1}),O(B,{label:"售后状态",align:"center",prop:"refundStatus",width:"100"},{default:T((e=>[O(I,{type:1===e.row.refundStatus?"warning":"info"},{default:T((()=>[V(U(1===e.row.refundStatus?"已售后":"未售后"),1)])),_:2},1032,["type"])])),_:1}),O(B,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:T((a=>[V(U(e.formatTime(a.row.createTime)),1)])),_:1}),O(B,{label:"操作",align:"center",width:"180","class-name":"small-padding fixed-width"},{default:T((e=>[O(d,{type:"primary",size:"small",onClick:a=>{return l=e.row,q.value=l,H.value=`服务订单详情 - ${l.orderSn}`,void(G.value=!0);var l}},{default:T((()=>[V("查看")])),_:2},1032,["onClick"]),O(d,{type:"danger",size:"small",onClick:a=>(async e=>{const a=e?[e]:n.value;if(0!==a.length)try{await A.confirm("确定要删除选中的服务订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await R(a.join(","));200===e.code?(p.success("删除成功"),K()):p.error(e.message||"删除失败")}catch(l){"cancel"!==l&&p.error("删除失败")}})(e.row.id)},{default:T((()=>[V("删除")])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[oe,l.value]]),N("div",F,[O(ae,{"current-page":o.current,"onUpdate:currentPage":s[2]||(s[2]=e=>o.current=e),"page-size":o.size,"onUpdate:pageSize":s[3]||(s[3]=e=>o.size=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:o.total,onSizeChange:Y,onCurrentChange:ee},null,8,["current-page","page-size","total"])])]),O(re,{modelValue:G.value,"onUpdate:modelValue":s[5]||(s[5]=e=>G.value=e),title:H.value,width:"50%","close-on-click-modal":!1},{footer:T((()=>[N("span",L,[O(d,{onClick:s[4]||(s[4]=e=>G.value=!1)},{default:T((()=>[V("关闭")])),_:1})])])),default:T((()=>[O(te,{column:1,border:""},{default:T((()=>[O(le,{label:"订单号"},{default:T((()=>[V(U(q.value.orderSn),1)])),_:1}),O(le,{label:"店铺名称"},{default:T((()=>[V(U(q.value.mallName),1)])),_:1}),O(le,{label:"服务名称"},{default:T((()=>[V(U(q.value.serviceName),1)])),_:1}),O(le,{label:"服务规格"},{default:T((()=>[V(U(q.value.skuSpec),1)])),_:1}),O(le,{label:"实付价格"},{default:T((()=>[V(U(e.formatAmount(q.value.amount)),1)])),_:1}),O(le,{label:"支付状态"},{default:T((()=>[O(I,{type:1===q.value.payStatus?"success":"danger"},{default:T((()=>[V(U(1===q.value.payStatus?"已支付":"未支付"),1)])),_:1},8,["type"])])),_:1}),O(le,{label:"创建时间"},{default:T((()=>[V(U(e.formatTime(q.value.createTime)),1)])),_:1}),O(le,{label:"支付时间"},{default:T((()=>[V(U(e.formatTime(q.value.payTime)),1)])),_:1})])),_:1})])),_:1},8,["modelValue","title"])])}},__scopeId:"data-v-70d8cc95"};export{Z as default};
