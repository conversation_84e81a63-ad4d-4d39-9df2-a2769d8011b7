var e=Object.defineProperty,a=Object.defineProperties,t=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,o=(a,t,l)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[t]=l;import{Z as n,a8 as i,_ as c,ao as u,ac as p,a3 as d,af as f,ag as g,a9 as v,am as m,as as y,o as b,k as h,l as j,m as w,at as z,F as O,w as _,an as x,v as k,a6 as C,a7 as N,aO as P}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                   *//* empty css                 *//* empty css                      *//* empty css                    *//* empty css                  *//* empty css                        */import{i as S}from"./index.34be5c94.js";import"./RefreshButton.c2d64dc7.js";import{A as V}from"./ActionBar.acef8039.js";const I=e=>{const n=(i=((e,a)=>{for(var t in a||(a={}))r.call(a,t)&&o(e,t,a[t]);if(l)for(var t of l(a))s.call(a,t)&&o(e,t,a[t]);return e})({},e),c={pageNum:e.page,pageSize:e.size},a(i,t(c)));var i,c;return delete n.page,delete n.size,S.get("/runningLog/pageQuery",{params:n})},R=e=>S.get("/runningLog/viewLog?fileName="+e);const T={class:"list-container"},L=(e=>(C("data-v-2c6b56fc"),e=e(),N(),e))((()=>j("div",{class:"search-area"},null,-1))),U={class:"search-area"},$={class:"pagination-container"},A={class:"dialog-footer"},B={__name:"List",setup(e){const a=n([]),t=n(!1),l=n(null);n("");const r=n(!1);i({type:"",name:"",review:"",status:""});const s=i({current:1,size:10,total:0}),o=n([]),C=n(!0),N=n(!0),S=e=>{o.value=e.map((e=>e.id)),C.value=1!=e.length,N.value=!e.length};c((()=>{B()}));const B=async()=>{t.value=!0;try{const e={page:s.current,size:s.size},l=await I(e);200===l.code?(a.value=l.data.list||[],s.total=l.data.total||0):u.error(l.message||"获取数据失败")}catch(e){console.error("获取数据失败:",e),"ECONNABORTED"===e.code?u.error("请求超时，请检查网络连接或联系管理员"):e.response?u.error(`请求失败: ${e.response.status} ${e.response.statusText}`):e.request?u.error("服务器未响应，请稍后再试"):u.error(`请求错误: ${e.message}`),a.value=[],s.total=0}finally{t.value=!1}},E=n(),q=n(null);let D=null;const F=async e=>{const a=await R(e);E.value=a.data,P((()=>{var e,a;const t=null==(a=null==(e=q.value)?void 0:e.$el)?void 0:a.querySelector("textarea");t&&(t.scrollTop=t.scrollHeight)}))},H=()=>{clearInterval(D),D=null,r.value=!1,E.value=""},Q=()=>{B()},Z=e=>{s.size=e,s.current=1,B()},G=e=>{s.current=e,B()};return(e,o)=>{const n=p,i=d,c=f,u=g,C=v,N=m,P=y;return b(),h("div",T,[L,j("div",U,[w(V,{onRefresh:Q}),z((b(),O(c,{ref_key:"tableRef",ref:l,data:a.value,border:"",stripe:"",style:{width:"100%"},onSelectionChange:S},{default:_((()=>[w(n,{type:"selection",width:"55",align:"center"}),x("",!0),w(n,{label:"文件名称",align:"center",prop:"fileName"}),w(n,{label:"文件类型",align:"center",prop:"fileType"}),w(n,{label:"排序",align:"center",prop:"fileOrder"}),w(n,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:_((e=>[w(i,{type:"primary",onClick:a=>(async e=>{clearInterval(D),D=null,E.value="",r.value=!0,await F(e),D=setInterval((async()=>{await F(e)}),5e3)})(e.row.fileName)},{default:_((()=>[k("查看日志")])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[P,t.value]]),j("div",$,[w(u,{"current-page":s.current,"onUpdate:currentPage":o[0]||(o[0]=e=>s.current=e),"page-size":s.size,"onUpdate:pageSize":o[1]||(o[1]=e=>s.size=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:s.total,onSizeChange:Z,onCurrentChange:G},null,8,["current-page","page-size","total"])])]),w(N,{modelValue:r.value,"onUpdate:modelValue":o[3]||(o[3]=e=>r.value=e),width:"1300px","close-on-click-modal":!1},{footer:_((()=>[j("span",A,[w(i,{onClick:H},{default:_((()=>[k("关闭")])),_:1})])])),default:_((()=>[w(C,{modelValue:E.value,"onUpdate:modelValue":o[2]||(o[2]=e=>E.value=e),style:{width:"100%"},rows:30,type:"textarea",placeholder:"暂无日志信息",disabled:"",ref_key:"logTextareaRef",ref:q},null,8,["modelValue"])])),_:1},8,["modelValue"])])}},__scopeId:"data-v-2c6b56fc"};export{B as default};
