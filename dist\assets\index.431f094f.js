import{a as e,c as t,E as o,d as s,g as r,u as n,b as a,e as i,f as c,h as l,i as m,j as d,o as u,k as p,l as h,m as _,w as f,n as g,p as k,t as T,s as v,q as E,r as I,v as S,x as y,y as b,z as A,A as L,B as w,C as P,D as O,F as R,G as j,H as C,I as N,J as x,K as D,L as V,M as W,N as U,O as $,P as M,Q as z,R as F,S as J,T as B,U as H,V as K,W as q,X as G,Y as Q}from"./vendor.9a6f3141.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerpolicy&&(t.referrerPolicy=e.referrerpolicy),"use-credentials"===e.crossorigin?t.credentials="include":"anonymous"===e.crossorigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const X=e.create({baseURL:"/api",timeout:3e4});const Y=new class{constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=3e3,this.listeners=new Map}connect(){if(!this.socket||this.socket.readyState!==WebSocket.OPEN&&this.socket.readyState!==WebSocket.CONNECTING)try{const e=localStorage.getItem("accessToken");if(!e)return void console.error("WebSocket连接失败: 未找到accessToken");this.socket=new WebSocket("ws://146.56.192.164:9090/ws?token="+e),this.socket.onopen=()=>{console.log("WebSocket连接已建立"),this.isConnected=!0,this.reconnectAttempts=0,this.sendMessage({type:"auth",token:e}),this.triggerEvent("connect")},this.socket.onmessage=e=>{try{const t=JSON.parse(e.data);console.log("收到WebSocket消息:",t),this.triggerEvent("message",t)}catch(t){console.error("解析WebSocket消息失败:",t),this.triggerEvent("error",t)}},this.socket.onclose=e=>{console.log("WebSocket连接已关闭:",e),this.isConnected=!1,this.triggerEvent("disconnect"),this.attemptReconnect()},this.socket.onerror=e=>{console.error("WebSocket错误:",e),this.triggerEvent("error",e)}}catch(e){console.error("初始化WebSocket失败:",e),this.triggerEvent("error",e)}else console.log("WebSocket已连接或正在连接中")}attemptReconnect(){this.reconnectAttempts>=this.maxReconnectAttempts?console.log("达到最大重连次数"):(this.reconnectAttempts++,console.log(`尝试第 ${this.reconnectAttempts} 次重连...`),setTimeout((()=>{this.connect()}),this.reconnectInterval))}sendMessage(e){if(!this.socket||this.socket.readyState!==WebSocket.OPEN)return console.error("WebSocket未连接，无法发送消息"),!1;try{const t="string"==typeof e?e:JSON.stringify(e);return this.socket.send(t),!0}catch(t){return console.error("发送WebSocket消息失败:",t),!1}}disconnect(){this.socket&&(this.socket.close(),this.socket=null,this.isConnected=!1)}on(e,t){this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(t)}off(e,t){if(!this.listeners.has(e))return;const o=this.listeners.get(e),s=o.indexOf(t);-1!==s&&o.splice(s,1)}triggerEvent(e,t){if(!this.listeners.has(e))return;this.listeners.get(e).forEach((o=>{try{o(t)}catch(s){console.error(`执行${e}事件监听器出错:`,s)}}))}};var Z=t({state:{accessToken:localStorage.getItem("accessToken")||"",refreshToken:localStorage.getItem("refreshToken")||"",userInfo:JSON.parse(localStorage.getItem("userInfo"))||null},mutations:{SET_TOKEN(e,t){e.accessToken=t.accessToken,e.refreshToken=t.refreshToken,localStorage.setItem("accessToken",e.accessToken),localStorage.setItem("refreshToken",e.refreshToken)},CLEAR_AUTH(e){e.accessToken="",e.refreshToken="",e.userInfo=null,localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("userInfo"),Y.disconnect()},SET_USER_INFO(e,t){e.userInfo=t,localStorage.setItem("userInfo",JSON.stringify(e.userInfo))}},actions:{async login({commit:e},t){try{const s=await ee.login(t);if(console.log("响应",s),200!==s.code)throw new Error(s.message||"登录失败");e("SET_TOKEN",s.data),console.log("登录成功，准备连接WebSocket"),console.log("accessToken值:",s.data.accessToken),s.data.accessToken?Y.connect(s.data.accessToken):console.error("无法连接WebSocket: accessToken不存在");try{const t=await ee.getAdmin();if(200!==t.code)return console.error("获取用户信息失败:",t.message),Promise.resolve("登录成功，但获取用户信息失败");e("SET_USER_INFO",t.data)}catch(o){return console.error("获取用户信息出错:",o),Promise.resolve("登录成功，但获取用户信息失败")}return Promise.resolve("登录成功")}catch(s){return e("CLEAR_AUTH"),Promise.reject(s)}},logout({commit:e}){e("CLEAR_AUTH")}},getters:{isAuthenticated:e=>!!e.accessToken}});const ee={register:e=>X.post("/admin/register",e),login:e=>X.post("/admin/login",e),getAdmin:()=>X.get("/admin/getAdmin")};X.interceptors.request.use((e=>{const t=localStorage.getItem("accessToken");return t&&(e.headers.Authorization=`Bearer ${t}`),e}),(e=>(console.log("请求错误",e),Promise.reject(e)))),function(e){let t=!1,s=[];const r=(e="登录已过期，请重新登录")=>{o.confirm(e,"提示",{confirmButtonText:"重新登录",type:"warning",showCancelButton:!1,center:!0}).then((()=>{Z.dispatch("logout"),window.location.href="/login"}))};e.interceptors.response.use((e=>{const{data:t}=e;if(200!=t.code){const e=new Error(t.message||"业务逻辑错误");return e.name="BusinessError",e.data=t,e.code=t.code,Promise.reject(e)}return t}),(async o=>{const n=o.config;if(!o.response)return Promise.reject(o);const a=o.response.status;if(console.log("statusCode",a),(401===a||500===a)&&!n._retry){if(n._retry=!0,console.log("isRefreshing",t),t)return new Promise((t=>{s.push((o=>{n.headers.Authorization=`Bearer ${o}`,t(e(n))}))}));t=!0;try{const a=localStorage.getItem("refreshToken");if(!a)return r(),Promise.reject(o);const i=new FormData;i.append("refreshToken",a);const c=await X.post("/admin/refreshToken",i);console.log("刷新token响应:",c);const l=c.data||c;console.log("响应数据:",l);const m=l.accessToken,d=l.refreshToken;if(!m)throw new Error("刷新token失败：未获取到新的accessToken");return localStorage.setItem("accessToken",m),d&&localStorage.setItem("refreshToken",d),n.headers.Authorization=`Bearer ${m}`,s.forEach((e=>e(m))),s=[],e(n)}catch(i){return r(),Promise.reject(i)}finally{t=!1}}return Promise.reject(o)}))}(X);var te=t({state:{accessToken:localStorage.getItem("accessToken")||"",refreshToken:localStorage.getItem("refreshToken")||"",userInfo:JSON.parse(localStorage.getItem("userInfo"))||null},mutations:{SET_TOKEN(e,t){e.accessToken=t.accessToken,e.refreshToken=t.refreshToken,localStorage.setItem("accessToken",e.accessToken),localStorage.setItem("refreshToken",e.refreshToken)},CLEAR_AUTH(e){e.accessToken="",e.refreshToken="",e.userInfo=null,localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("userInfo"),Y.disconnect()},SET_USER_INFO(e,t){e.userInfo=t,localStorage.setItem("userInfo",JSON.stringify(e.userInfo))}},actions:{async login({commit:e},t){try{const s=await ee.login(t);if(console.log("响应",s),200!==s.code)throw new Error(s.message||"登录失败");e("SET_TOKEN",s.data),console.log("登录成功，准备连接WebSocket"),console.log("accessToken值:",s.data.accessToken),s.data.accessToken?Y.connect(s.data.accessToken):console.error("无法连接WebSocket: accessToken不存在");try{const t=await ee.getAdmin();if(200!==t.code)return console.error("获取用户信息失败:",t.message),Promise.resolve("登录成功，但获取用户信息失败");e("SET_USER_INFO",t.data)}catch(o){return console.error("获取用户信息出错:",o),Promise.resolve("登录成功，但获取用户信息失败")}return Promise.resolve("登录成功")}catch(s){return e("CLEAR_AUTH"),Promise.reject(s)}},logout({commit:e}){e("CLEAR_AUTH")}},getters:{isAuthenticated:e=>!!e.accessToken}});const oe={class:"navbar"},se={class:"left-panel"},re={class:"system-title"},ne={class:"right-panel"},ae={class:"user-avatar"},ie={class:"user-name"};var ce=s({__name:"Navbar",setup(e){var t;const o=null==(t=r())?void 0:t.appContext.config.globalProperties.$global,s=JSON.parse(localStorage.getItem("userInfo")),L=(null==s?void 0:s.username)||"admin",w=n(),P=e=>{w.push(e)},O=async()=>{confirm("确定要退出吗？")&&(await te.dispatch("logout"),w.replace("/login"))};return(e,t)=>{var s;const r=A,n=a,w=i,R=c,j=l,C=m,N=d;return u(),p("div",oe,[h("div",se,[_(r,{size:24},{default:f((()=>[_(g(k))])),_:1}),h("span",re,T(null==(s=g(o))?void 0:s.system.name)+"管理系统",1)]),h("div",ne,[_(n,{content:"系统设置"},{default:f((()=>[_(r,{size:20},{default:f((()=>[_(g(v))])),_:1})])),_:1}),_(w,{value:5,class:"message-badge"},{default:f((()=>[_(n,{content:"消息中心"},{default:f((()=>[_(r,{size:20},{default:f((()=>[_(g(E))])),_:1})])),_:1})])),_:1}),_(N,null,{dropdown:f((()=>[_(C,null,{default:f((()=>[_(j,{onClick:t[0]||(t[0]=e=>P("/user/center"))},{default:f((()=>[_(r,null,{default:f((()=>[_(g(I))])),_:1}),S(" 个人中心 ")])),_:1}),_(j,{onClick:t[1]||(t[1]=e=>P("/log"))},{default:f((()=>[_(r,null,{default:f((()=>[_(g(y))])),_:1}),S(" 日志中心 ")])),_:1}),_(j,{divided:"",onClick:O},{default:f((()=>[_(r,null,{default:f((()=>[_(g(b))])),_:1}),S(" 退出登录 ")])),_:1})])),_:1})])),default:f((()=>[h("div",ae,[_(R,{size:32,src:"https://example.com/avatar.jpg"}),h("span",ie,T(g(L)),1)])])),_:1})])])}}});ce.__scopeId="data-v-5b6dffae";var le=s({__name:"Sidebar",setup(e){const t=L([{title:"系统管理",path:"/system",icon:v,children:[{title:"配置列表",path:"/settledConfig/list"},{title:"会员开通记录",path:"/settledConfig/memberRecord"},{title:"用户列表",path:"/user/list"},{title:"角色管理",path:"/user/role"},{title:"权限管理",path:"/user/permission"},{title:"邀请列表",path:"/invitation/list"},{title:"操作日志",path:"/log/operate"},{title:"登录日志",path:"/log/login"}]},{title:"店铺管理",path:"/shop",icon:N,children:[{title:"店铺列表",path:"/shop/list"}]},{title:"书品管理",path:"/book",icon:x,children:[{title:"选品中心",path:"/book/selection/center"}]},{title:"仓储管理",path:"/warehouse",icon:D,children:[{title:"货区列表",path:"/warehouse/depot/list"}]},{title:"工具管理",path:"/tools",icon:y,children:[{title:"卡密列表",path:"/tools/cards/list"},{title:"活跃卡密列表",path:"/tools/cards/activeCardsList"}]},{title:"审核管理",path:"/examine",children:[{title:"违规列表",path:"/examine/violation/list"}]},{title:"日志管理",path:"/log",children:[{title:"日志列表",path:"/log/runningLog/list"}]},{title:"任务管理",path:"/task",icon:V,children:[{title:"任务列表",path:"/task/list"}]},{title:"功能模块",path:"/useModule",children:[{title:"服务列表",path:"/useModule/vas/list"}]},{title:"监控中心",path:"/monitor",icon:W,children:[{title:"监控大屏",path:"/monitor/dashboard"}]}]);return(e,o)=>{const s=A,r=w,n=P,a=O;return u(),R(a,{router:"","default-active":e.$route.path,collapse:!1,"unique-opened":"","background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF"},{default:f((()=>[(u(!0),p(C,null,j(t.value,(e=>(u(),R(n,{key:e.path,index:e.path},{title:f((()=>[_(s,null,{default:f((()=>[(u(),R(U(e.icon)))])),_:2},1024),h("span",null,T(e.title),1)])),default:f((()=>[(u(!0),p(C,null,j(e.children,(e=>(u(),R(r,{key:e.path,index:e.path},{default:f((()=>[S(T(e.title),1)])),_:2},1032,["index"])))),128))])),_:2},1032,["index"])))),128))])),_:1},8,["default-active"])}}});le.__scopeId="data-v-67773242";var me=s({__name:"Index",setup:e=>(e,t)=>{const o=$,s=M,r=z("router-view"),n=F,a=J;return u(),R(a,{class:"layout-container"},{default:f((()=>[_(o,{height:"60px"},{default:f((()=>[_(ce)])),_:1}),_(a,null,{default:f((()=>[_(s,{width:"220px"},{default:f((()=>[_(le)])),_:1}),_(n,{style:{padding:"5px"}},{default:f((()=>[_(r)])),_:1})])),_:1})])),_:1})}});me.__scopeId="data-v-e2946954";var de=Object.freeze({__proto__:null,[Symbol.toStringTag]:"Module",default:me});const ue={__name:"App",setup(e){const t=B(),o=H((()=>!t.meta.noLayout));return(e,t)=>{const s=z("router-view");return o.value?(u(),R(me,{key:0},{default:f((()=>[_(s)])),_:1})):(u(),R(s,{key:1}))}}},pe={},he=function(e,t){return t&&0!==t.length?Promise.all(t.map((e=>{if((e=`/${e}`)in pe)return;pe[e]=!0;const t=e.endsWith(".css"),o=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${o}`))return;const s=document.createElement("link");return s.rel=t?"stylesheet":"modulepreload",t||(s.as="script",s.crossOrigin=""),s.href=e,document.head.appendChild(s),t?new Promise(((e,t)=>{s.addEventListener("load",e),s.addEventListener("error",t)})):void 0}))).then((()=>e())):e()};function _e(){return X({url:"/admin/permission/tree",method:"get"})}function fe(e){return X({url:"/admin/permission/add",method:"post",data:e})}function ge(e){return X({url:"/admin/permission/update",method:"put",data:e})}function ke(e){return X({url:`/admin/permission/delete/${e}`,method:"delete"})}let Te=[];function ve(e){return!e||Te.includes(e)}const Ee=[{path:"/",component:()=>he((()=>Promise.resolve().then((function(){return de}))),void 0),children:[{path:"",component:()=>he((()=>import("./TabsView.86bf0ca0.js")),["assets/TabsView.86bf0ca0.js","assets/TabsView.c3702853.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css"]),meta:{noLayout:!0},children:[{path:"/welcome",component:()=>he((()=>import("./Index.4dff6204.js")),["assets/Index.4dff6204.js","assets/Index.ce5cde9d.css","assets/el-card.40323e49.css","assets/el-col.40817b40.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css"]),meta:{title:"欢迎"}},{path:"/SettledConfig/list",component:()=>he((()=>import("./List.ce67e4d6.js")),["assets/List.ce67e4d6.js","assets/List.c01b5947.css","assets/el-overlay.7e3bbad5.css","assets/el-switch.5c2a972f.css","assets/el-divider.0e977bc9.css","assets/el-input.7b677563.css","assets/el-input-number.65f9b1ef.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-pagination.52030e07.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/ActionBar.acef8039.js","assets/ActionBar.24c81c8e.css","assets/RefreshButton.c2d64dc7.js"]),meta:{title:"配置列表",permission:"settled:config:list"}},{path:"/SettledConfig/memberRecord",component:()=>he((()=>import("./MemberRecord.0f62e00a.js")),["assets/MemberRecord.0f62e00a.js","assets/MemberRecord.85ee488d.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-input.7b677563.css","assets/el-input-number.65f9b1ef.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-pagination.52030e07.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/el-date-picker.c37c0117.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/RefreshButton.c2d64dc7.js","assets/ActionBar.24c81c8e.css"]),meta:{title:"会员开通记录",permission:"settled:member:record"}},{path:"/user/list",component:()=>he((()=>import("./List.d4e165ec.js")),["assets/List.d4e165ec.js","assets/List.41ac4dca.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-form-item.fd530480.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-pagination.52030e07.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/role.d5608d50.js"]),meta:{title:"用户列表",permission:"user:list:view"}},{path:"/user/role",component:()=>he((()=>import("./Role.9f561333.js")),["assets/Role.9f561333.js","assets/Role.4d15255f.css","assets/el-loading.6eef1391.css","assets/el-popconfirm.ef94d602.css","assets/el-checkbox.583d4ee9.css","assets/el-overlay.7e3bbad5.css","assets/el-form-item.fd530480.css","assets/el-switch.5c2a972f.css","assets/el-input.7b677563.css","assets/el-card.40323e49.css","assets/el-table-column.4d034af6.css","assets/el-popover.103bfbf9.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/role.d5608d50.js"]),meta:{title:"角色管理",permission:"user:role:manage"}},{path:"/user/permission",component:()=>he((()=>import("./Permission.d25e01e3.js")),["assets/Permission.d25e01e3.js","assets/Permission.32ea98f8.css","assets/el-overlay.7e3bbad5.css","assets/el-form-item.fd530480.css","assets/el-switch.5c2a972f.css","assets/el-input.7b677563.css","assets/el-input-number.65f9b1ef.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-card.40323e49.css","assets/el-popconfirm.ef94d602.css","assets/el-popover.103bfbf9.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css"]),meta:{title:"权限管理",permission:"user:permission:manage"}},{path:"/invitation/list",component:()=>he((()=>import("./index.52859644.js")),["assets/index.52859644.js","assets/index.80f06c37.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-card.40323e49.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css"]),meta:{title:"邀请列表",permission:"invitation:list:view"}},{path:"/shop/list",component:()=>he((()=>import("./index.fb131670.js")),["assets/index.fb131670.js","assets/index.cba02d07.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-radio.ac8ebaf8.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-switch.5c2a972f.css","assets/el-form-item.fd530480.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/RefreshButton.c2d64dc7.js","assets/ActionBar.24c81c8e.css"]),meta:{title:"店铺列表",permission:"shop:list:view"}},{path:"/book/selection/center",component:()=>he((()=>import("./index.004c3fb8.js")),["assets/index.004c3fb8.js","assets/index.3ab89b99.css","assets/el-loading.6eef1391.css","assets/el-checkbox.583d4ee9.css","assets/el-overlay.7e3bbad5.css","assets/el-form-item.fd530480.css","assets/el-progress.e041cf4a.css","assets/el-col.40817b40.css","assets/el-pagination.52030e07.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-popover.103bfbf9.css","assets/el-input-number.65f9b1ef.css","assets/el-date-picker.c37c0117.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/RefreshButton.c2d64dc7.js","assets/ActionBar.24c81c8e.css"]),meta:{title:"选品中心",permission:"book:selection:view"}},{path:"/warehouse/depot/list",component:()=>he((()=>import("./List.751c5f1e.js")),["assets/List.751c5f1e.js","assets/List.105316c5.css","assets/el-loading.6eef1391.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-overlay.7e3bbad5.css","assets/el-form-item.fd530480.css","assets/el-radio.ac8ebaf8.css","assets/el-pagination.52030e07.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css"]),meta:{title:"货区管理",permission:"warehouse:depot:view"}},{path:"/tools/cards/list",component:()=>he((()=>import("./List.7f2c46ad.js")),["assets/List.7f2c46ad.js","assets/List.cfff731a.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/el-date-picker.c37c0117.css","assets/el-input-number.65f9b1ef.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/cards.d615f538.js","assets/RefreshButton.c2d64dc7.js","assets/ActionBar.24c81c8e.css","assets/ActionBar.acef8039.js"]),meta:{title:"卡密列表",permission:"cards:list:view"}},{path:"/tools/cards/activeCardsList",component:()=>he((()=>import("./ActiveCardsList.38690473.js")),["assets/ActiveCardsList.38690473.js","assets/ActiveCardsList.d2072a49.css","assets/el-loading.6eef1391.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/ActionBar.acef8039.js","assets/ActionBar.24c81c8e.css","assets/RefreshButton.c2d64dc7.js","assets/cards.d615f538.js"]),meta:{title:"活跃卡密列表",permission:"cards:active:view"}},{path:"/examine/violation/list",component:()=>he((()=>import("./List.3ce9ea14.js")),["assets/List.3ce9ea14.js","assets/List.b375e6dd.css","assets/el-loading.6eef1391.css","assets/el-checkbox.583d4ee9.css","assets/el-overlay.7e3bbad5.css","assets/el-pagination.52030e07.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/RefreshButton.c2d64dc7.js","assets/ActionBar.24c81c8e.css","assets/ActionBar.acef8039.js"]),meta:{title:"违规列表"}},{path:"/log/runningLog/list",component:()=>he((()=>import("./List.aa152400.js")),["assets/List.aa152400.js","assets/List.31eb8f1d.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-input.7b677563.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-table-column.4d034af6.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/RefreshButton.c2d64dc7.js","assets/ActionBar.24c81c8e.css","assets/ActionBar.acef8039.js"]),meta:{title:"运行日志",permission:"log:running:view"}},{path:"/task/list",component:()=>he((()=>import("./List.f8f552c2.js")),["assets/List.f8f552c2.js","assets/List.97c1ba35.css","assets/el-overlay.7e3bbad5.css","assets/el-descriptions-item.11af55f0.css","assets/el-progress.e041cf4a.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-card.40323e49.css","assets/el-table-column.4d034af6.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css"]),meta:{title:"任务列表",permission:"task:list:view"}},{path:"/useModule/vas/list",component:()=>he((()=>import("./List.09f5bbae.js")),["assets/List.09f5bbae.js","assets/List.880ae485.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-descriptions-item.11af55f0.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css","assets/ActionBar.acef8039.js","assets/ActionBar.24c81c8e.css","assets/RefreshButton.c2d64dc7.js"]),meta:{title:"服务列表",permission:"vas:list:view"}},{path:"/monitor/dashboard",component:()=>he((()=>import("./Dashboard.c41f91ef.js")),["assets/Dashboard.c41f91ef.js","assets/Dashboard.456bc7a2.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css"]),meta:{title:"监控大屏",permission:"monitor:dashboard:view"}}]}]},{path:"/login",component:()=>he((()=>import("./Index.cac9b3f3.js")),["assets/Index.cac9b3f3.js","assets/Index.932715bd.css","assets/el-form-item.fd530480.css","assets/el-input.7b677563.css","assets/el-divider.0e977bc9.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css"]),meta:{noLayout:!0,public:!0}},{path:"/redirectUrl",component:()=>he((()=>import("./index.b20ccb6f.js")),["assets/index.b20ccb6f.js","assets/index.f00cd3c6.css","assets/el-form-item.fd530480.css","assets/el-input.7b677563.css","assets/vendor.9a6f3141.js","assets/vendor.f9422048.css"]),meta:{noLayout:!0,title:"用户注册",public:!0}}],Ie=K({history:q(),routes:Ee});Ie.beforeEach(((e,t,o)=>{if(e.meta&&e.meta.permission){const t=Te,s=e.meta.permission;t.includes(s)?o():o("/403")}else o()}));const Se={mounted(e,t){const{value:o}=t;if(o){let t=!1;t=Array.isArray(o)?!(s=o)||0===s.length||s.some((e=>ve(e))):ve(o),t||e.parentNode&&e.parentNode.removeChild(e)}var s}},ye={mounted(e,t){const{value:o}=t;if(o&&Array.isArray(o)){o.every((e=>ve(e)))||e.parentNode&&e.parentNode.removeChild(e)}}},be=G(ue);be.directive("permission",Se),be.directive("permission-all",ye),be.use(te),be.use(Ie),be.use(Q),async function(){try{const e=await X({url:"/admin/permission/user/codes",method:"get"});200===e.code&&(Te=e.data.filter((e=>e&&""!==e.trim())))}catch(e){console.error("获取用户权限失败:",e)}}().then((()=>{be.mount("#app")}));export{fe as a,ke as d,_e as g,X as i,Z as s,ge as u};
