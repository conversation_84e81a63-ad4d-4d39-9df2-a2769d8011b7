import{Z as e,a8 as a,$ as t,_ as l,ao as o,a9 as r,aa as i,ar as n,a3 as s,ab as c,ac as d,ae as u,af as p,ag as m,ah as k,ai as b,aj as g,am as f,as as y,o as h,k as v,l as V,m as _,w as x,v as C,at as T,F as w,t as N,an as I,H as D,G as J,a6 as F,a7 as R,E as U}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                   *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css                      *//* empty css                        *//* empty css                     *//* empty css                       */import{i as j}from"./index.34be5c94.js";import{R as S}from"./RefreshButton.c2d64dc7.js";const M=e=>j.get("/settledMember/record/listWithDetails",{params:e}),O=e=>j.post("/settledMember/record/delete",null,{params:{id:e}}),z=e=>(console.log("API调用 - 请求数据:",JSON.stringify(e,null,2)),j.put("/settledMember/record/update",e,{headers:{"Content-Type":"application/json;charset=UTF-8"}})),P=(e,a)=>(console.log("发送状态更新请求:",{id:e,state:a}),j.post("/settledMember/record/updateStatus",null,{params:{id:e,state:a}}));const q=e=>(F("data-v-57db99ea"),e=e(),R(),e),B={class:"list-container"},Y={class:"search-area"},$={class:"action-bar"},K=q((()=>V("div",{class:"action-left"},null,-1))),H={class:"action-right"},A={class:"expand-content"},E={class:"expand-item"},G=q((()=>V("span",{class:"expand-label"},"会员限制:",-1))),L={key:0},W={key:1},Z={key:0,class:"expand-item"},Q=q((()=>V("span",{class:"expand-label"},"备注:",-1))),X={class:"pagination-container"},ee=q((()=>V("span",{class:"form-tip"},"%",-1))),ae=q((()=>V("span",{class:"form-tip"},"分",-1))),te={class:"form-tip"},le=q((()=>V("span",{class:"form-tip"},"万分比",-1))),oe={class:"constraint-inputs"},re={class:"constraint-input-item"},ie=q((()=>V("span",{class:"constraint-label"},"最小本数:",-1))),ne={class:"constraint-input-item"},se=q((()=>V("span",{class:"constraint-label"},"最大本数:",-1))),ce={class:"dialog-footer"},de={__name:"MemberRecord",setup(F){const R=e([]),j=e(!1),q=e(null),de=e([]),ue=a({userId:"",memberName:"",phoneNumber:"",timeRange:[]}),pe=[{text:"最近一周",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-6048e5),[a,e]}},{text:"最近一个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-2592e6),[a,e]}},{text:"最近三个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-7776e6),[a,e]}}],me=a({current:1,size:20,total:0}),ke=e([]),be=e(!1),ge=e("add"),fe=e(null),ye=e(!1),he=a({id:null,userId:"",settledCostConfigId:null,title:"",settledCostKey:"",constraintJson:'{"books_count_min":0,"books_count_max":3000}',expirationDate:"",openTime:"",kickbackType:1,kickbackValue:0,kickbackPoint:0,kickbackFixed:0,resourceCostType:1,resourceCostValue:0,serviceRate:0,price:0,state:0,note:"",createdBy:0,updatedBy:0}),ve={userId:[{required:!0,message:"请输入用户ID",trigger:"blur"}],settledCostConfigId:[{required:!0,message:"请选择配置",trigger:"change"}],title:[{required:!0,message:"请输入标题",trigger:"blur"},{max:20,message:"标题长度不能超过20个字符",trigger:"blur"}],expirationDate:[{required:!0,message:"请选择到期时间",trigger:"change"}],kickbackType:[{required:!0,message:"请选择佣金类型",trigger:"change"}],kickbackValue:[{required:!0,message:"请输入佣金值",trigger:"blur"}],resourceCostType:[{required:!0,message:"请选择资源占用费类型",trigger:"change"}],resourceCostValue:[{required:!0,message:"请输入资源占用费值",trigger:"blur"}],serviceRate:[{required:!0,message:"请输入服务费比例",trigger:"blur"}],price:[{required:!0,message:"请输入购买价格",trigger:"blur"}],state:[{required:!0,message:"请选择状态",trigger:"change"}]},Ve=e(0),_e=e(3e3),xe=()=>{he.constraintJson=JSON.stringify({books_count_min:Ve.value,books_count_max:_e.value})};t((()=>he.constraintJson),(e=>{if(e)try{const a=JSON.parse(e);Ve.value=void 0!==a.books_count_min?a.books_count_min:0,_e.value=void 0!==a.books_count_max?a.books_count_max:3e3}catch(a){console.error("解析约束条件失败:",a)}}),{immediate:!0}),l((()=>{Ce()}));const Ce=async()=>{j.value=!0;try{console.log("fetchData中的当前页码:",me.current);const e={pageNum:me.current,pageSize:me.size,userId:ue.userId||void 0,memberName:ue.memberName||void 0,phoneNumber:ue.phoneNumber||void 0};ue.timeRange&&2===ue.timeRange.length&&(e.startTime=ue.timeRange[0],e.endTime=ue.timeRange[1]),console.log("最终发送的分页请求参数:",JSON.stringify(e));const a=await M(e);console.log("获取分页数据响应:",a),200===a.code&&a.data&&(R.value=a.data.list||[],me.total=a.data.total||0)}catch(e){console.error("获取数据失败:",e),o.error("获取数据失败")}finally{j.value=!1}},Te=()=>{Ce()},we=()=>{me.current=1,Ce()},Ne=()=>{ue.userId="",ue.memberName="",ue.phoneNumber="",ue.timeRange=[],me.current=1,Ce()},Ie=e=>{console.log("改变每页显示数量:",e),me.size=e,me.current=1,Ce()},De=e=>{console.log("改变页码，值为:",e),me.current=e,setTimeout((()=>{console.log("即将请求的页码:",me.current),Ce()}),0)},Je=e=>{de.value=e},Fe=async(e,a)=>{e.loading=!0;const t=0===a?"启用":"停用";U.confirm(`确定要${t}该记录吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await P(e.id,a),o.success(`${t}成功`),Ce()}catch(l){console.error(`${t}失败:`,l),o.error(`${t}失败: ${l.message||"未知错误"}`)}finally{e.loading=!1}})).catch((()=>{e.loading=!1}))},Re=e=>{const a=ke.value.find((a=>a.id===e));a&&(he.title=a.title,he.settledCostKey=a.settledCostKey,he.price=a.price/100,a.constraintJson&&(he.constraintJson=a.constraintJson))},Ue=async()=>{fe.value&&await fe.value.validate((async e=>{var a,t,l,r;if(e){ye.value=!0;try{Pe();const e={id:Number(he.id),userId:Number(he.userId),settledCostConfigId:Number(he.settledCostConfigId),title:(null==(a=he.title)?void 0:a.trim())||"",settledCostKey:(null==(t=he.settledCostKey)?void 0:t.trim())||"",constraintJson:he.constraintJson,expirationDate:new Date(he.expirationDate).getTime(),kickbackType:Number(he.kickbackType),kickbackValue:he.kickbackValue,resourceCostType:Number(he.resourceCostType),resourceCostValue:Number(Math.round(he.resourceCostValue)),serviceRate:Number(Math.round(he.serviceRate)),price:Number(Math.round(100*he.price)),state:Number(he.state),note:(null==(l=he.note)?void 0:l.trim())||""};console.log("提交数据(完整):",JSON.stringify(e,null,2)),console.log("kickbackValue类型:",typeof e.kickbackValue);const r=await z(e);console.log("更新响应:",r),o.success("更新成功"),be.value=!1,Ce()}catch(i){if(console.error("API错误:",i),null==(r=i.response)?void 0:r.data){console.error("错误响应数据:",i.response.data);const e=i.response.data;let a="提交失败";"string"==typeof e?a=e:e.message?a=e.message:e.error&&(a=e.error),o.error(a)}else o.error("提交失败: "+(i.message||"未知错误"))}finally{ye.value=!1}}}))},je=()=>{fe.value&&fe.value.resetFields(),Object.assign(he,{id:null,userId:"",settledCostConfigId:null,title:"",settledCostKey:"",constraintJson:'{"books_count_min":0,"books_count_max":3000}',expirationDate:"",openTime:"",kickbackType:1,kickbackValue:0,kickbackPoint:0,kickbackFixed:0,resourceCostType:1,resourceCostValue:0,serviceRate:0,price:0,state:0,note:"",createdBy:0,updatedBy:0}),Ve.value=0,_e.value=3e3},Se=e=>{if(!e)return"-";return new Date(e).toLocaleString()},Me=e=>({0:"已启用",1:"已停用"}[e]||"未知"),Oe=(e,a)=>{if(1===a){try{if("string"==typeof e&&e.includes("{")){return(JSON.parse(e).point/1e4).toFixed(2)+"%"}}catch(t){console.error("解析佣金值失败:",t)}return(e/1e4).toFixed(2)+"%"}if(2===a){try{if("string"==typeof e&&e.includes("{")){return(JSON.parse(e).fixed/100).toFixed(2)+"元"}}catch(t){console.error("解析佣金值失败:",t)}return(e/100).toFixed(2)+"元"}if(3===a)try{if("string"==typeof e&&e.includes("{")){const a=JSON.parse(e);return`提点: ${(a.point/1e4).toFixed(2)}%, 固定: ${(a.fixed/100).toFixed(2)}元`}}catch(t){console.error("解析佣金值失败:",t)}return e},ze=(e,a,t)=>{try{const l=JSON.parse(e);return void 0!==l[a]?l[a]:t}catch(l){return console.error("解析约束条件失败:",l),t}},Pe=()=>{try{let e={};e=1===he.kickbackType?{point:Math.round(1e4*he.kickbackPoint),fixed:0}:2===he.kickbackType?{point:0,fixed:Math.round(he.kickbackFixed)}:3===he.kickbackType?{point:Math.round(1e4*he.kickbackPoint),fixed:Math.round(he.kickbackFixed)}:{point:0,fixed:0},he.kickbackValue=JSON.stringify(e),console.log("更新后的佣金值:",he.kickbackValue,"类型:",typeof he.kickbackValue)}catch(e){console.error("更新佣金值失败:",e),he.kickbackValue=JSON.stringify({point:0,fixed:0})}};return t((()=>he.kickbackType),(()=>{Pe()})),(e,a)=>{const t=r,l=i,F=n,M=s,z=c,P=d,de=u,qe=p,Be=m,Ye=k,$e=b,Ke=g,He=f,Ae=y;return h(),v("div",B,[V("div",Y,[_(z,{inline:!0,model:ue},{default:x((()=>[_(l,{label:"会员ID"},{default:x((()=>[_(t,{modelValue:ue.userId,"onUpdate:modelValue":a[0]||(a[0]=e=>ue.userId=e),placeholder:"请输入会员ID",clearable:""},null,8,["modelValue"])])),_:1}),_(l,{label:"会员名称"},{default:x((()=>[_(t,{modelValue:ue.memberName,"onUpdate:modelValue":a[1]||(a[1]=e=>ue.memberName=e),placeholder:"请输入会员名称",clearable:""},null,8,["modelValue"])])),_:1}),_(l,{label:"手机号"},{default:x((()=>[_(t,{modelValue:ue.phoneNumber,"onUpdate:modelValue":a[2]||(a[2]=e=>ue.phoneNumber=e),placeholder:"请输入手机号",clearable:""},null,8,["modelValue"])])),_:1}),_(l,{label:"开通时间"},{default:x((()=>[_(F,{modelValue:ue.timeRange,"onUpdate:modelValue":a[3]||(a[3]=e=>ue.timeRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",shortcuts:pe},null,8,["modelValue"])])),_:1}),_(l,null,{default:x((()=>[_(M,{type:"primary",onClick:we},{default:x((()=>[C("搜索")])),_:1}),_(M,{onClick:Ne},{default:x((()=>[C("重置")])),_:1})])),_:1})])),_:1},8,["model"])]),V("div",$,[K,V("div",H,[_(S,{onRefresh:Te})])]),T((h(),w(qe,{ref_key:"tableRef",ref:q,data:R.value,border:"",stripe:"",style:{width:"100%"},onSelectionChange:Je,"row-key":"id"},{default:x((()=>[_(P,{type:"expand"},{default:x((({row:e})=>[V("div",A,[V("div",E,[G,e.constraintJson?(h(),v("span",L," 最小本数: "+N(ze(e.constraintJson,"books_count_min",0))+"， 最大本数: "+N(ze(e.constraintJson,"books_count_max",3e3))+"， 当前本数: "+N(e.booksCount||0),1)):(h(),v("span",W,"-"))]),e.note?(h(),v("div",Z,[Q,V("span",null,N(e.note),1)])):I("",!0)])])),_:1}),_(P,{type:"selection",align:"center",width:"55"}),_(P,{prop:"id",align:"center",label:"记录ID",width:"80"}),_(P,{prop:"userId",align:"center",label:"用户ID",width:"100"}),_(P,{prop:"title",align:"center",label:"会员名称",width:"120"}),_(P,{prop:"phoneNumber",align:"center",label:"手机号",width:"120"}),_(P,{prop:"settledCostKey",align:"center",label:"入驻标识",width:"150"}),_(P,{align:"center",label:"创建时间",width:"180"},{default:x((({row:e})=>[C(N(e.createdTime?Se(e.createdTime):"-"),1)])),_:1}),_(P,{align:"center",label:"更新时间",width:"180"},{default:x((({row:e})=>[C(N(e.updatedTime?Se(e.updatedTime):"-"),1)])),_:1}),_(P,{align:"center",label:"到期时间",width:"180"},{default:x((({row:e})=>[C(N(Se(e.expirationDate)),1)])),_:1}),_(P,{align:"center",label:"佣金设置",width:"200"},{default:x((({row:e})=>{return[C(N((a=e.kickbackType,{0:"预留",1:"提点",2:"固定费用",3:"提点/固费"}[a]||"未知"))+": "+N(Oe(e.kickbackValue,e.kickbackType)),1)];var a})),_:1}),_(P,{align:"center",label:"资源占用费",width:"200"},{default:x((({row:e})=>{return[C(N((a=e.resourceCostType,{0:"预留",1:"提点",2:"固定费用"}[a]||"未知"))+": "+N(Oe(e.resourceCostValue,e.resourceCostType)),1)];var a})),_:1}),_(P,{prop:"serviceRate",align:"center",label:"服务费比例",width:"120"},{default:x((({row:e})=>[C(N((e.serviceRate/1e4).toFixed(2))+"% ",1)])),_:1}),_(P,{prop:"price",align:"center",label:"购买价格(元)",width:"120"},{default:x((({row:e})=>[C(N((e.price/100).toFixed(2)),1)])),_:1}),_(P,{prop:"state",align:"center",label:"状态",width:"100"},{default:x((({row:e})=>{return[_(de,{type:(a=e.state,{0:"success",1:"danger"}[a]||"info")},{default:x((()=>[C(N(Me(e.state)),1)])),_:2},1032,["type"])];var a})),_:1}),_(P,{align:"center",label:"操作",fixed:"right",width:"220"},{default:x((({row:e})=>[_(M,{size:"small",type:"primary",onClick:a=>(e=>{je(),ge.value="edit",Object.assign(he,e),he.price=e.price/100;try{if("string"==typeof e.kickbackValue&&e.kickbackValue.includes("{")){const a=JSON.parse(e.kickbackValue);he.kickbackPoint=a.point/1e4||0,he.kickbackFixed=a.fixed||0}else 1===e.kickbackType?(he.kickbackPoint=parseFloat(e.kickbackValue)/1e4||0,he.kickbackFixed=0):2===e.kickbackType?(he.kickbackPoint=0,he.kickbackFixed=parseFloat(e.kickbackValue)||0):3===e.kickbackType&&(he.kickbackPoint=0,he.kickbackFixed=0)}catch(a){console.error("解析佣金值失败:",a),he.kickbackPoint=0,he.kickbackFixed=0}try{if(e.constraintJson){const a=JSON.parse(e.constraintJson);Ve.value=void 0!==a.books_count_min?a.books_count_min:0,_e.value=void 0!==a.books_count_max?a.books_count_max:3e3}}catch(a){console.error("解析约束条件失败:",a)}be.value=!0})(e)},{default:x((()=>[C("编辑")])),_:2},1032,["onClick"]),_(M,{size:"small",type:"danger",onClick:a=>(e=>{U.confirm("确定要删除该记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await O(e.id),o.success("删除成功"),Ce()}catch(a){console.error("删除失败:",a),o.error("删除失败")}})).catch((()=>{}))})(e),disabled:1===e.state},{default:x((()=>[C("删除")])),_:2},1032,["onClick","disabled"]),0===e.state?(h(),w(M,{key:0,size:"small",type:"warning",onClick:a=>Fe(e,1),loading:e.loading},{default:x((()=>[C("停用")])),_:2},1032,["onClick","loading"])):I("",!0),1===e.state?(h(),w(M,{key:1,size:"small",type:"success",onClick:a=>Fe(e,0),loading:e.loading},{default:x((()=>[C("启用")])),_:2},1032,["onClick","loading"])):I("",!0)])),_:1})])),_:1},8,["data"])),[[Ae,j.value]]),V("div",X,[_(Be,{"current-page":me.current,"page-size":me.size,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:me.total,onSizeChange:Ie,onCurrentChange:De},null,8,["current-page","page-size","total"])]),_(He,{title:"add"===ge.value?"新增入驻会员开通记录":"编辑入驻会员开通记录",modelValue:be.value,"onUpdate:modelValue":a[20]||(a[20]=e=>be.value=e),width:"600px","close-on-click-modal":!1},{footer:x((()=>[V("span",ce,[_(M,{onClick:a[19]||(a[19]=e=>be.value=!1)},{default:x((()=>[C("取消")])),_:1}),_(M,{type:"primary",onClick:Ue,loading:ye.value},{default:x((()=>[C("确定")])),_:1},8,["loading"])])])),default:x((()=>[_(z,{ref_key:"formRef",ref:fe,model:he,rules:ve,"label-width":"120px","label-position":"right"},{default:x((()=>[_(l,{label:"会员ID",prop:"userId"},{default:x((()=>[_(t,{modelValue:he.userId,"onUpdate:modelValue":a[4]||(a[4]=e=>he.userId=e),placeholder:"请输入会员ID"},null,8,["modelValue"])])),_:1}),_(l,{label:"会员名称",prop:"title"},{default:x((()=>[_(t,{modelValue:he.title,"onUpdate:modelValue":a[5]||(a[5]=e=>he.title=e),placeholder:"请输入会员名称",maxlength:"20","show-word-limit":""},null,8,["modelValue"])])),_:1}),_(l,{label:"配置选择",prop:"settledCostConfigId"},{default:x((()=>[_($e,{modelValue:he.settledCostConfigId,"onUpdate:modelValue":a[6]||(a[6]=e=>he.settledCostConfigId=e),placeholder:"请选择配置",onChange:Re},{default:x((()=>[(h(!0),v(D,null,J(ke.value,(e=>(h(),w(Ye,{key:e.id,label:e.title,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),_(l,{label:"到期时间",prop:"expirationDate"},{default:x((()=>[_(F,{modelValue:he.expirationDate,"onUpdate:modelValue":a[7]||(a[7]=e=>he.expirationDate=e),type:"datetime",placeholder:"选择到期时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])])),_:1}),_(l,{label:"支付金额(元)",prop:"price"},{default:x((()=>[_(Ke,{modelValue:he.price,"onUpdate:modelValue":a[8]||(a[8]=e=>he.price=e),min:0,precision:2,step:10,placeholder:"请输入支付金额"},null,8,["modelValue"])])),_:1}),_(l,{label:"佣金类型",prop:"kickbackType"},{default:x((()=>[_($e,{modelValue:he.kickbackType,"onUpdate:modelValue":a[9]||(a[9]=e=>he.kickbackType=e),placeholder:"请选择佣金类型"},{default:x((()=>[_(Ye,{label:"预留",value:0}),_(Ye,{label:"提点",value:1}),_(Ye,{label:"固定费用",value:2}),_(Ye,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(he.kickbackType)?(h(),w(l,{key:0,label:"提点比例",prop:"kickbackPoint"},{default:x((()=>[_(Ke,{modelValue:he.kickbackPoint,"onUpdate:modelValue":a[10]||(a[10]=e=>he.kickbackPoint=e),min:0,precision:2,step:.01,placeholder:"请输入提点比例",onChange:Pe},null,8,["modelValue"]),ee])),_:1})):I("",!0),[2,3].includes(he.kickbackType)?(h(),w(l,{key:1,label:"固定费用",prop:"kickbackFixed"},{default:x((()=>[_(Ke,{modelValue:he.kickbackFixed,"onUpdate:modelValue":a[11]||(a[11]=e=>he.kickbackFixed=e),min:0,precision:2,step:1,placeholder:"请输入固定费用",onChange:Pe},null,8,["modelValue"]),ae])),_:1})):I("",!0),_(l,{label:"资源占用费类型",prop:"resourceCostType"},{default:x((()=>[_($e,{modelValue:he.resourceCostType,"onUpdate:modelValue":a[12]||(a[12]=e=>he.resourceCostType=e),placeholder:"请选择资源占用费类型"},{default:x((()=>[_(Ye,{label:"预留",value:0}),_(Ye,{label:"提点",value:1}),_(Ye,{label:"固定费用",value:2})])),_:1},8,["modelValue"])])),_:1}),_(l,{label:"资源占用费值",prop:"resourceCostValue"},{default:x((()=>[_(Ke,{modelValue:he.resourceCostValue,"onUpdate:modelValue":a[13]||(a[13]=e=>he.resourceCostValue=e),min:0,precision:1===he.resourceCostType?2:0,step:1===he.resourceCostType?.01:1,placeholder:"请输入资源占用费值"},null,8,["modelValue","precision","step"]),V("span",te,N(1===he.resourceCostType?"万分比":"分"),1)])),_:1}),_(l,{label:"服务费比例",prop:"serviceRate"},{default:x((()=>[_(Ke,{modelValue:he.serviceRate,"onUpdate:modelValue":a[14]||(a[14]=e=>he.serviceRate=e),min:0,precision:2,step:.01,placeholder:"请输入服务费比例"},null,8,["modelValue"]),le])),_:1}),_(l,{label:"会员限制",prop:"constraintJson"},{default:x((()=>[V("div",oe,[V("div",re,[ie,_(Ke,{modelValue:Ve.value,"onUpdate:modelValue":a[15]||(a[15]=e=>Ve.value=e),min:0,precision:0,onChange:xe},null,8,["modelValue"])]),V("div",ne,[se,_(Ke,{modelValue:_e.value,"onUpdate:modelValue":a[16]||(a[16]=e=>_e.value=e),min:0,precision:0,onChange:xe},null,8,["modelValue"])])])])),_:1}),_(l,{label:"备注",prop:"note"},{default:x((()=>[_(t,{modelValue:he.note,"onUpdate:modelValue":a[17]||(a[17]=e=>he.note=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1}),_(l,{label:"状态",prop:"state"},{default:x((()=>[_($e,{modelValue:he.state,"onUpdate:modelValue":a[18]||(a[18]=e=>he.state=e),placeholder:"请选择状态"},{default:x((()=>[_(Ye,{label:"已启用",value:0}),_(Ye,{label:"已停用",value:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["title","modelValue"])])}},__scopeId:"data-v-57db99ea"};export{de as default};
