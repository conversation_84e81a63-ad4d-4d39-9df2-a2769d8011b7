<template>
    <div class="register-page">
        <div class="register-container">
            <div class="register-form">
                <!-- 标题 -->
                <div class="form-header">
                    <h2>与书同行</h2>
                    <!-- <div class="language-switch">
                        <span>A</span>
                    </div> -->
                </div>

                <!-- 表单 -->
                <el-form ref="formRef" :model="formData" :rules="rules" label-width="0">
                    <!-- 用户名 -->
                    <el-form-item prop="username">
                        <el-input
                            v-model="formData.username"
                            placeholder="用户名"
                            size="large"
                            :prefix-icon="User"
                        />
                    </el-form-item>

                    <!-- 手机号 -->
                    <el-form-item prop="phoneNumber">
                        <el-input
                            v-model="formData.phoneNumber"
                            placeholder="手机号"
                            size="large"
                            :prefix-icon="Phone"
                        />
                    </el-form-item>

                    <!-- 密码 -->
                    <el-form-item prop="password">
                        <el-input
                            v-model="formData.password"
                            type="password"
                            placeholder="密码"
                            size="large"
                            :prefix-icon="Lock"
                            show-password
                        />
                    </el-form-item>

                    <!-- 确认密码 -->
                    <el-form-item prop="confirmPassword">
                        <el-input
                            v-model="formData.confirmPassword"
                            type="password"
                            placeholder="确认密码"
                            size="large"
                            :prefix-icon="Lock"
                            show-password
                        />
                    </el-form-item>

                    <!-- 邀请码 -->
                    <el-form-item prop="inviteCode">
                        <el-input
                            v-model="formData.inviteCode"
                            placeholder="邀请码"
                            size="large"
                            :prefix-icon="Message"
                        />
                    </el-form-item>

                    <!-- 验证码 -->
                    <el-form-item v-if="captchaEnabled" prop="code">
                        <div class="captcha-row">
                            <el-input
                                v-model="formData.code"
                                placeholder="验证码"
                                size="large"
                                :prefix-icon="Key"
                            />
                            <div class="captcha-image" @click="getCaptcha">
                                <img v-if="captchaImg" :src="captchaImg" alt="验证码" />
                            </div>
                        </div>
                    </el-form-item>

                    <!-- 注册按钮 -->
                    <el-form-item>
                        <el-button
                            type="primary"
                            size="large"
                            class="register-btn"
                            @click="handleRegister"
                            :loading="loading"
                        >
                            注册
                        </el-button>
                    </el-form-item>

                    <!-- 登录链接 -->
                    <div class="login-link">
                        <span>使用已有账户登录</span>
                    </div>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Phone, Lock, Message, Key } from '@element-plus/icons-vue'

const route = useRoute()
const loading = ref(false)
const formRef = ref()
const captchaImg = ref('')
const captchaEnabled = ref(true)

// URL参数
const urlParams = ref({
    pddMallId: '',
    pddMallName: '',
    type: '',
    accessToken: '',
    skuSpec: ''
})

// 表单数据
const formData = ref({
    username: '',
    phoneNumber: '',
    password: '',
    confirmPassword: '',
    inviteCode: '',
    code: '',
    uuid: '',
    clientId: 'e5cd7e4891bf95d1d19206ce24a7b32e',
    grantType: 'password',
    tenantId: '000000',
    userType: 'sys_user'
})

// 表单验证规则
const rules = {
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 2, max: 20, message: '用户名长度在2到20个字符', trigger: 'blur' }
    ],
    phoneNumber: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 5, max: 20, message: '密码长度在5到20个字符', trigger: 'blur' }
    ],
    confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        {
            validator: (rule, value, callback) => {
                if (value !== formData.value.password) {
                    callback(new Error('两次输入密码不一致'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ],
    code: [
        {
            required: true,
            message: '请输入验证码',
            trigger: 'blur',
            validator: (rule, value, callback) => {
                // 如果验证码未启用，直接通过验证
                if (!captchaEnabled.value) {
                    callback()
                    return
                }
                // 如果验证码启用但为空，则验证失败
                if (!value) {
                    callback(new Error('请输入验证码'))
                } else {
                    callback()
                }
            }
        }
    ]
}

// 获取URL参数
const getUrlParams = () => {
    const query = route.query
    urlParams.value = {
        pddMallId: query.pddMallId || '',
        pddMallName: decodeURIComponent(query.pddMallName || ''),
        type: query.type || '',
        accessToken: query.accessToken || '',
        skuSpec: decodeURIComponent(query.skuSpec || '')
    }

    // 将 "ppd" + pddMallId 回填到用户名
    if (urlParams.value.pddMallId) {
        formData.value.username = 'pdd' + urlParams.value.pddMallId
    }

    console.log('获取到的URL参数:', urlParams.value)
}

// 获取验证码
const getCaptcha = async () => {
    try {
        const response = await fetch('/auth/code', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })

        if (response.ok) {
            // 检查响应内容类型
            const contentType = response.headers.get('content-type')
            if (contentType && contentType.includes('application/json')) {
                const result = await response.json()
                if (result.code === 200) {
                    const { data } = result

                    // 检查是否启用验证码
                    captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled

                    if (captchaEnabled.value) {
                        // 设置验证码图片和UUID
                        formData.value.uuid = data.uuid
                        captchaImg.value = 'data:image/gif;base64,' + data.img
                    } else {
                        // 如果验证码未启用，清空相关字段
                        formData.value.uuid = ''
                        captchaImg.value = ''
                        // 可以隐藏验证码输入框或显示提示
                        console.log('验证码功能未启用')
                    }
                } else {
                    ElMessage.error(result.msg || '获取验证码失败')
                }
            } else {
                // 服务器返回的不是JSON格式
                const text = await response.text()
                console.error('服务器返回非JSON响应:', text)
                ElMessage.error('服务器响应格式错误，请检查API接口')
            }
        } else {
            // HTTP状态码不是200-299
            console.error(`HTTP错误: ${response.status} ${response.statusText}`)
            ElMessage.error(`获取验证码失败 (${response.status})，请重试`)
        }
    } catch (error) {
        console.error('获取验证码失败:', error)
        if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
            ElMessage.error('服务器响应格式错误，请检查API接口')
        } else {
            ElMessage.error('网络错误，获取验证码失败')
        }
    }
}

// 注册处理
const handleRegister = async () => {
    try {
        // 表单验证
        const valid = await formRef.value.validate()
        if (!valid) return

        loading.value = true

        // 构建注册数据
        const registerData = {
            username: formData.value.username,
            password: formData.value.password,
            phoneNumber: formData.value.phoneNumber,
            inviteCode: formData.value.inviteCode,
            clientId: formData.value.clientId,
            grantType: formData.value.grantType,
            tenantId: formData.value.tenantId,
            userType: formData.value.userType,
            // 拼多多相关参数
            pddMallId: urlParams.value.pddMallId,
            pddMallName: urlParams.value.pddMallName,
            pddType: urlParams.value.type,
            accessToken: urlParams.value.accessToken,
            skuSpec: urlParams.value.skuSpec
        }

        // 只在验证码启用时添加验证码相关字段
        if (captchaEnabled.value) {
            registerData.code = formData.value.code
            registerData.uuid = formData.value.uuid
        }

        console.log('注册数据:', registerData)

        // 调用注册API
        const response = await fetch('/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(registerData)
        })

        const result = await response.json()

        if (result.code === 200) {
            // 复制链接到剪贴板
            const loginUrl = 'https://erp.buzhiyushu.cn/'
            try {
                await navigator.clipboard.writeText(loginUrl)
                ElMessage.success('注册成功！登录链接https://erp.buzhiyushu.cn/已复制到剪贴板')
            } catch (err) {
                // 如果复制失败，显示链接让用户手动复制
                ElMessage.success(`注册成功！请复制链接：${loginUrl}`)
            }
            // 注册成功后可以跳转到登录页面或其他页面
            // router.push('/login')
        } else {
            ElMessage.error(result.msg || '注册失败，请重试')
            // 如果是验证码相关错误，重新获取验证码
            if (captchaEnabled.value && result.msg &&
                (result.msg.includes('验证码') || result.msg.includes('captcha'))) {
                getCaptcha()
                // 清空验证码输入框
                formData.value.code = ''
            }
        }

    } catch (error) {
        console.error('注册失败:', error)
        ElMessage.error('注册失败，请重试')
    } finally {
        loading.value = false
    }
}

// 页面加载时获取URL参数和验证码
onMounted(() => {
    getUrlParams()
    getCaptcha()
})
</script>

<style scoped>
.register-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.register-container {
    width: 100%;
    max-width: 400px;
}

.register-form {
    background: white;
    border-radius: 12px;
    padding: 40px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.form-header h2 {
    font-size: 24px;
    color: #333;
    margin: 0;
    font-weight: 500;
}

.language-switch {
    width: 32px;
    height: 32px;
    background: #f0f0f0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.language-switch span {
    font-size: 14px;
    color: #666;
    font-weight: bold;
}

.el-form-item {
    margin-bottom: 20px;
}

.el-input {
    border-radius: 8px;
}

:deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: 0 0 0 1px #dcdfe6;
    border: none;
}

:deep(.el-input__wrapper:hover) {
    box-shadow: 0 0 0 1px #c0c4cc;
}

:deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #409eff;
}



.captcha-row {
    display: flex;
    gap: 10px;
    align-items: center;
}

.captcha-row .el-input {
    flex: 1;
}

.captcha-image {
    width: 100px;
    height: 40px;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
}

.captcha-image img {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
}

.register-btn {
    width: 100%;
    height: 48px;
    font-size: 16px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.register-btn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.login-link {
    text-align: center;
    margin-top: 20px;
}

.login-link span {
    color: #666;
    font-size: 14px;
    cursor: pointer;
}

.login-link span:hover {
    color: #409eff;
}

/* 移动端适配 */
@media (max-width: 480px) {
    .register-form {
        padding: 30px 20px;
        margin: 10px;
    }

    .form-header h2 {
        font-size: 20px;
    }
}
</style>