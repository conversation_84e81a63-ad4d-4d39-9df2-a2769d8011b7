import{Z as e,a8 as l,_ as a,ao as t,a9 as o,ah as s,ai as i,ar as n,a3 as r,aj as d,ac as u,b as c,aC as p,aD as m,ae as b,af as v,ag as h,aa as f,a4 as g,aE as y,aF as k,ab as _,am as V,aG as x,as as w,o as S,k as P,l as I,m as U,w as T,n as C,au as N,v as j,aH as B,aq as z,t as M,aI as R,at as Y,F as A,aJ as D,aw as F,an as L,a6 as O,a7 as q,E as $,z as E}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                    *//* empty css                   *//* empty css                     *//* empty css                    *//* empty css               *//* empty css                      *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                   *//* empty css                        *//* empty css                       */import{i as J}from"./index.9f8b8a79.js";import{R as W}from"./RefreshButton.c2d64dc7.js";const G=e=>J.get("/baseInfo/list",{params:e}),H=e=>J.post("/book/baseInfo/add",e),Z=e=>J.put("/book/baseInfo/update",e),K=e=>J.delete(`/book/baseInfo/delete/${e}`),Q=e=>J.post("/book/baseInfo/batchDelete",{ids:e}),X=e=>J.put("/baseInfo/batchUpdateIll",e);const ee=e=>(O("data-v-1a0e5572"),e=e(),q(),e),le={class:"list-container"},ae={class:"search-area"},te={class:"search-row"},oe={class:"search-item"},se=ee((()=>I("span",{class:"search-label"},"书名",-1))),ie={class:"search-item"},ne=ee((()=>I("span",{class:"search-label"},"isbn",-1))),re={class:"search-item"},de=ee((()=>I("span",{class:"search-label"},"作者",-1))),ue={class:"search-item"},ce=ee((()=>I("span",{class:"search-label"},"出版社",-1))),pe={class:"search-row"},me={class:"search-item"},be=ee((()=>I("span",{class:"search-label"},"书图片",-1))),ve={class:"search-item date-range-item"},he=ee((()=>I("span",{class:"search-label"},"出版时间范围",-1))),fe={class:"search-item"},ge=ee((()=>I("span",{class:"search-label"},"违规信息筛选",-1))),ye={class:"search-item btn-item"},ke={class:"search-item"},_e=ee((()=>I("span",{class:"search-label"},"销量",-1))),Ve={class:"search-item number-range-item"},xe=ee((()=>I("span",{class:"search-label"},"已售",-1))),we=ee((()=>I("span",{class:"range-separator"},"至",-1))),Se={class:"search-item number-range-item"},Pe=ee((()=>I("span",{class:"search-label"},"在售",-1))),Ie=ee((()=>I("span",{class:"range-separator"},"至",-1))),Ue={class:"search-item btn-item"},Te={class:"toggle-advanced-search"},Ce={class:"action-bar"},Ne={class:"action-left"},je={class:"action-right"},Be={ref:"textRef",class:"ellipsis-text"},ze={class:"ellipsis-text"},Me={key:1,class:"no-image"},Re={ref:"textRef",class:"ellipsis-text"},Ye={class:"ellipsis-text"},Ae={ref:"textRef",class:"ellipsis-text"},De={class:"ellipsis-text"},Fe={ref:"textRef",class:"ellipsis-text"},Le={class:"ellipsis-text"},Oe={class:"sales-detail"},qe={class:"sales-item"},$e=ee((()=>I("span",{class:"sales-label"},"7天销量:",-1))),Ee={class:"sales-item"},Je=ee((()=>I("span",{class:"sales-label"},"15天销量:",-1))),We={class:"sales-item"},Ge=ee((()=>I("span",{class:"sales-label"},"30天销量:",-1))),He={class:"sales-item"},Ze=ee((()=>I("span",{class:"sales-label"},"60天销量:",-1))),Ke={class:"sales-item"},Qe=ee((()=>I("span",{class:"sales-label"},"90天销量:",-1))),Xe={class:"sales-item"},el=ee((()=>I("span",{class:"sales-label"},"180天销量:",-1))),ll={class:"sales-item"},al=ee((()=>I("span",{class:"sales-label"},"365天销量:",-1))),tl={class:"sales-item"},ol=ee((()=>I("span",{class:"sales-label"},"今年销量:",-1))),sl={class:"sales-item"},il=ee((()=>I("span",{class:"sales-label"},"去年销量:",-1))),nl={class:"sales-item"},rl=ee((()=>I("span",{class:"sales-label"},"总销量:",-1))),dl={class:"sales-trigger"},ul={key:1,class:"normal-text"},cl={class:"pagination-container"},pl=["src"],ml={key:0,class:"edit-disabled-tip"},bl={class:"dialog-footer"},vl={class:"violation-config"},hl={class:"violation-item"},fl=ee((()=>I("span",{class:"violation-label"},"违规书号",-1))),gl={class:"violation-item"},yl=ee((()=>I("span",{class:"violation-label"},"套装书",-1))),kl={class:"violation-item"},_l=ee((()=>I("span",{class:"violation-label"},"一号多书",-1))),Vl={class:"violation-item"},xl=ee((()=>I("span",{class:"violation-label"},"违规出版社",-1))),wl={class:"violation-item"},Sl=ee((()=>I("span",{class:"violation-label"},"违规作者",-1))),Pl={class:"dialog-footer"},Il={__name:"index",setup(O){const q=e([]),J=e(!1),ee=e(null),Il=e([]),Ul=l({bookName:"",isbn:"",author:"",publisher:"",timeRange:[],violationTypes:[],inventoryType:null,saleSelect:null,minInventory:0,maxInventory:999999,minSelling:0,maxSelling:999999,hasImage:null});e("");const Tl=e([]),Cl=e(0),Nl=e=>{if(!e.bookPic||"0"===e.bookPic)return 0;const l=ma(e);return Tl.value.findIndex((e=>e===l))},jl=[{text:"最近一年",value:()=>{const e=new Date,l=new Date;return l.setFullYear(l.getFullYear()-1),[l,e]}},{text:"最近三年",value:()=>{const e=new Date,l=new Date;return l.setFullYear(l.getFullYear()-3),[l,e]}},{text:"最近五年",value:()=>{const e=new Date,l=new Date;return l.setFullYear(l.getFullYear()-5),[l,e]}}],Bl=l({current:1,size:20,total:0}),zl=e(!1),Ml=e("add"),Rl=e(null),Yl=e(!1),Al=e(!1),Dl=e(!1),Fl=l({vioBook:!1,bookSet:!1,onenumMbooks:!1,illPublisher:!1,illAuthor:!1}),Ll=e("/api/upload"),Ol=l({id:null,tenantId:"000000",bookName:"",isbn:"",author:"",publisher:"",bookPic:"",editor:"",bindingLayout:"",edition:"",publicationTime:"",pages:"",fixPrice:"",content:"",remark:"",vioBook:0,bookSet:0,onenumMbooks:0,illPublisher:0,illAuthor:0}),ql={bookName:[{required:!0,message:"请输入书名",trigger:"blur"},{max:400,message:"书名长度不能超过400个字符",trigger:"blur"}],isbn:[{required:!0,message:"请输入ISBN",trigger:"blur"},{max:30,message:"ISBN长度不能超过30个字符",trigger:"blur"}],author:[{required:!0,message:"请输入作者",trigger:"blur"},{max:400,message:"作者长度不能超过400个字符",trigger:"blur"}],publisher:[{required:!0,message:"请输入出版社",trigger:"blur"},{max:200,message:"出版社长度不能超过200个字符",trigger:"blur"}],publicationTime:[{required:!0,message:"请选择出版时间",trigger:"change"}]},$l=e(!1),El=()=>{$l.value=!$l.value};a((()=>{Hl()}));const Jl=()=>{Bl.current=1,Ul.inventoryType=null,Ul.saleSelect=null,Ul.minInventory=0,Ul.maxInventory=999999,Ul.minSelling=0,Ul.maxSelling=999999,Hl(!1)},Wl=()=>{Bl.current=1,Hl(!0)},Gl=()=>{Hl($l.value)},Hl=async(e=!1)=>{J.value=!0;try{const l={pageNum:Bl.current,pageSize:Bl.size,bookName:Ul.bookName||void 0,isbn:Ul.isbn||void 0,author:Ul.author||void 0,publisher:Ul.publisher||void 0,vioBook:0,bookSet:0,onenumMbooks:0,illPublisher:0,illAuthor:0};if(Ul.timeRange&&2===Ul.timeRange.length&&(l.startTime=Ul.timeRange[0],l.endTime=Ul.timeRange[1]),null!==Ul.hasImage&&(l.bookPic=Ul.hasImage),Ul.violationTypes.length>0){const e={1:"vioBook",2:"bookSet",3:"onenumMbooks",4:"illPublisher",5:"illAuthor"};Ul.violationTypes.forEach((a=>{l[e[a]]=1}))}e&&(null!==Ul.inventoryType&&(l.inventoryType=Ul.inventoryType,l.saleSelect=null!==Ul.saleSelect?Ul.saleSelect:Ul.inventoryType),(Ul.minInventory>0||Ul.maxInventory<999999)&&(l.min1=Ul.minInventory.toString(),l.max1=Ul.maxInventory.toString()),(Ul.minSelling>0||Ul.maxSelling<999999)&&(l.min2=Ul.minSelling.toString(),l.max2=Ul.maxSelling.toString()));const a=await G(l);200===a.code&&a.data&&(q.value=a.data.list||[],Bl.total=a.data.total||0,Bl.current=a.data.pageNum||1,Bl.pages=a.data.pages||1)}catch(l){console.error("获取数据失败:",l),t.error("获取数据失败")}finally{J.value=!1}},Zl=()=>{Ul.bookName="",Ul.isbn="",Ul.author="",Ul.publisher="",Ul.timeRange=[],Ul.violationTypes=[],Ul.inventoryType=null,Ul.saleSelect=null,Ul.minInventory=0,Ul.maxInventory=999999,Ul.minSelling=0,Ul.maxSelling=999999,Ul.hasImage=null,Bl.current=1,Hl($l.value)},Kl=e=>{Bl.size=e,Bl.current=1,Hl($l.value)},Ql=e=>{Bl.current=e,Hl($l.value)},Xl=e=>{Il.value=e},ea=()=>{ua(),Ml.value="add",zl.value=!0},la=()=>{0!==Il.value.length?t.info("批量修改功能开发中"):t.warning("请至少选择一条记录")},aa=()=>{0!==Il.value.length?$.confirm(`确定要删除选中的${Il.value.length}条记录吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=Il.value.map((e=>e.id));await Q(e),t.success("删除成功"),Hl()}catch(e){console.error("删除失败:",e),t.error("删除失败")}})).catch((()=>{})):t.warning("请至少选择一条记录")},ta=()=>{0!==Il.value.length?(Object.keys(Fl).forEach((e=>{Fl[e]=!1})),Al.value=!0):t.warning("请至少选择一条记录")},oa=async()=>{Dl.value=!0;try{const e={ids:Il.value.map((e=>e.id)),vioBook:Fl.vioBook?1:0,bookSet:Fl.bookSet?1:0,onenumMbooks:Fl.onenumMbooks?1:0,illPublisher:Fl.illPublisher?1:0,illAuthor:Fl.illAuthor?1:0};await X(e),t.success("违规设置更新成功"),Al.value=!1,Hl()}catch(e){console.error("更新违规设置失败:",e),t.error("更新违规设置失败")}finally{Dl.value=!1}},sa=()=>{t.info("转价功能开发中")},ia=e=>{Ul.saleSelect=0===e?null:e},na=e=>{const l=e.type.startsWith("image/"),a=e.size/1024/1024<2;return l||t.error("上传封面图片只能是图片格式!"),a||t.error("上传封面图片大小不能超过 2MB!"),l&&a},ra=(e,l)=>{200===e.code?Ol.bookPic=e.data.url:t.error("上传失败: "+e.message)},da=async()=>{Rl.value&&await Rl.value.validate((async e=>{if(e){Yl.value=!0;try{const e=JSON.parse(JSON.stringify(Ol));e.fixPrice&&(e.fixPrice=Math.round(100*e.fixPrice)),"add"===Ml.value?(await H(e),t.success("添加成功")):(await Z(e),t.success("更新成功")),zl.value=!1,Hl()}catch(l){console.error("提交失败:",l),t.error("提交失败: "+(l.message||"未知错误"))}finally{Yl.value=!1}}}))},ua=()=>{Rl.value&&Rl.value.resetFields(),Object.assign(Ol,{id:null,tenantId:"000000",bookName:"",isbn:"",author:"",publisher:"",bookPic:"",editor:"",bindingLayout:"",edition:"",publicationTime:"",pages:"",fixPrice:"",content:"",remark:"",vioBook:0,bookSet:0,onenumMbooks:0,illPublisher:0,illAuthor:0})},ca=e=>1===e.vioBook||1===e.bookSet||1===e.onenumMbooks||1===e.illPublisher||1===e.illAuthor,pa=e=>{const l=[];return 1===e.vioBook&&l.push("违规书号"),1===e.bookSet&&l.push("套装书"),1===e.onenumMbooks&&l.push("一号多书"),1===e.illPublisher&&l.push("违规出版社"),1===e.illAuthor&&l.push("违规作者"),l.join("，")},ma=e=>{if(!e.bookPic)return"";if(e.bookPic){return"https://static.buzhiyushu.cn/images/"+e.bookPic}if(e.bookName&&e.isbn){return"https://book.goods.img.buzhiyushu.cn/"+md5(e.bookName).charAt(0)+"/"+e.isbn+"_01.jpg"}return""},ba=e=>{if(!e||!e.target)return!1;const l=e.target;return l.scrollWidth>l.offsetWidth};return(e,l)=>{const a=o,O=s,G=i,H=n,Z=E,Q=r,X=d,Il=u,va=c,ha=p,fa=m,ga=b,ya=v,ka=h,_a=f,Va=g,xa=y,wa=k,Sa=_,Pa=V,Ia=x,Ua=w;return S(),P("div",le,[I("div",ae,[I("div",te,[I("div",oe,[se,U(a,{modelValue:Ul.bookName,"onUpdate:modelValue":l[0]||(l[0]=e=>Ul.bookName=e),placeholder:"请输入书名",clearable:""},null,8,["modelValue"])]),I("div",ie,[ne,U(a,{modelValue:Ul.isbn,"onUpdate:modelValue":l[1]||(l[1]=e=>Ul.isbn=e),placeholder:"请输入isbn",clearable:""},null,8,["modelValue"])]),I("div",re,[de,U(a,{modelValue:Ul.author,"onUpdate:modelValue":l[2]||(l[2]=e=>Ul.author=e),placeholder:"请输入作者",clearable:""},null,8,["modelValue"])]),I("div",ue,[ce,U(a,{modelValue:Ul.publisher,"onUpdate:modelValue":l[3]||(l[3]=e=>Ul.publisher=e),placeholder:"请输入出版社",clearable:""},null,8,["modelValue"])])]),I("div",pe,[I("div",me,[be,U(G,{modelValue:Ul.hasImage,"onUpdate:modelValue":l[4]||(l[4]=e=>Ul.hasImage=e),placeholder:"请选择图片状态",clearable:""},{default:T((()=>[U(O,{label:"请选择图片状态",value:null}),U(O,{label:"有图片",value:1}),U(O,{label:"无图片",value:0})])),_:1},8,["modelValue"])]),I("div",ve,[he,U(H,{modelValue:Ul.timeRange,"onUpdate:modelValue":l[5]||(l[5]=e=>Ul.timeRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",shortcuts:jl},null,8,["modelValue"])]),I("div",fe,[ge,U(G,{modelValue:Ul.violationTypes,"onUpdate:modelValue":l[6]||(l[6]=e=>Ul.violationTypes=e),placeholder:"请选择违规条件",clearable:"",multiple:"",style:{width:"240px"},"collapse-tags":"","collapse-tags-tooltip":""},{default:T((()=>[U(O,{label:"违规书号",value:1}),U(O,{label:"套装书",value:2}),U(O,{label:"一号多书",value:3}),U(O,{label:"违规出版社",value:4}),U(O,{label:"违规作者",value:5})])),_:1},8,["modelValue"])]),I("div",ye,[U(Q,{type:"primary",onClick:Jl,class:"search-btn"},{default:T((()=>[U(Z,null,{default:T((()=>[U(C(N))])),_:1}),j(" 搜索 ")])),_:1})])]),I("div",{class:B(["search-row",{"advanced-search-hidden":!$l.value}])},[I("div",ke,[_e,U(G,{modelValue:Ul.inventoryType,"onUpdate:modelValue":l[7]||(l[7]=e=>Ul.inventoryType=e),placeholder:"请选择销量类型",clearable:"",onChange:ia},{default:T((()=>[U(O,{label:"请选择销量类型",value:null}),U(O,{label:"7天销量",value:7}),U(O,{label:"15天销量",value:15}),U(O,{label:"30天销量",value:30}),U(O,{label:"60天销量",value:60}),U(O,{label:"90天销量",value:90}),U(O,{label:"180天销量",value:180}),U(O,{label:"365天销量",value:365}),U(O,{label:"今年销量",value:0})])),_:1},8,["modelValue"])]),I("div",Ve,[xe,U(X,{modelValue:Ul.minInventory,"onUpdate:modelValue":l[8]||(l[8]=e=>Ul.minInventory=e),min:0,placeholder:"最小值","controls-position":"right"},null,8,["modelValue"]),we,U(X,{modelValue:Ul.maxInventory,"onUpdate:modelValue":l[9]||(l[9]=e=>Ul.maxInventory=e),min:0,placeholder:"最大值","controls-position":"right"},null,8,["modelValue"])]),I("div",Se,[Pe,U(X,{modelValue:Ul.minSelling,"onUpdate:modelValue":l[10]||(l[10]=e=>Ul.minSelling=e),min:0,placeholder:"最小值","controls-position":"right"},null,8,["modelValue"]),Ie,U(X,{modelValue:Ul.maxSelling,"onUpdate:modelValue":l[11]||(l[11]=e=>Ul.maxSelling=e),min:0,placeholder:"最大值","controls-position":"right"},null,8,["modelValue"])]),I("div",Ue,[U(Q,{onClick:Zl,class:"reset-btn"},{default:T((()=>[U(Z,null,{default:T((()=>[U(C(z))])),_:1}),j(" 重置 ")])),_:1}),U(Q,{type:"primary",onClick:Wl,plain:"",class:"adv-search-btn"},{default:T((()=>[j("高级搜索")])),_:1})])],2),I("div",Te,[U(Q,{type:"text",onClick:El},{default:T((()=>[j(M($l.value?"收起高级搜索":"展开高级搜索")+" ",1),U(Z,{class:B({"rotate-icon":$l.value})},{default:T((()=>[U(C(R))])),_:1},8,["class"])])),_:1})])]),I("div",Ce,[I("div",Ne,[U(Q,{type:"primary",onClick:ea},{default:T((()=>[j("新增")])),_:1}),U(Q,{type:"primary",onClick:la},{default:T((()=>[j("修改")])),_:1}),U(Q,{type:"danger",onClick:aa},{default:T((()=>[j("删除")])),_:1}),U(Q,{type:"primary",onClick:ta},{default:T((()=>[j("违规设置")])),_:1}),U(Q,{type:"primary",onClick:sa},{default:T((()=>[j("核价")])),_:1})]),I("div",je,[U(W,{onRefresh:Gl})])]),Y((S(),A(ya,{ref_key:"tableRef",ref:ee,data:q.value,border:"",style:{width:"100%"},onSelectionChange:Xl,"row-key":"id","header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",textAlign:"center"},height:"500","max-height":"500"},{default:T((()=>[U(Il,{type:"selection",align:"center",width:"50"}),U(Il,{prop:"bookName",align:"center",label:"书名","show-overflow-tooltip":""},{default:T((({row:l})=>[I("div",Be,[U(va,{content:l.bookName||"-",placement:"top",disabled:!ba(e.$event),enterable:!1},{default:T((()=>[I("span",ze,M(l.bookName||"-"),1)])),_:2},1032,["content","disabled"])],512)])),_:1}),U(Il,{align:"center",label:"书图片",width:"80"},{default:T((({row:e})=>[e.bookPic&&"0"!==e.bookPic?(S(),A(ha,{key:0,src:ma(e),"preview-src-list":Tl.value,"initial-index":Nl(e),style:{width:"40px",height:"60px","object-fit":"cover"},onClick:l=>(e=>{Tl.value=q.value.filter((e=>e.bookPic&&"0"!==e.bookPic)).map((e=>ma(e))),Cl.value=Tl.value.findIndex((l=>l===ma(e)))})(e),fit:"cover","preview-teleported":""},null,8,["src","preview-src-list","initial-index","onClick"])):(S(),P("span",Me,"无图"))])),_:1}),U(Il,{prop:"isbn",align:"center",label:"isbn",width:"120","show-overflow-tooltip":""},{default:T((({row:l})=>[I("div",Re,[U(va,{content:l.isbn||"-",placement:"top",disabled:!ba(e.$event),enterable:!1},{default:T((()=>[I("span",Ye,M(l.isbn||"-"),1)])),_:2},1032,["content","disabled"])],512)])),_:1}),U(Il,{prop:"author",align:"center",label:"作者","show-overflow-tooltip":""},{default:T((({row:l})=>[I("div",Ae,[U(va,{content:l.author||"-",placement:"top",disabled:!ba(e.$event),enterable:!1},{default:T((()=>[I("span",De,M(l.author||"-"),1)])),_:2},1032,["content","disabled"])],512)])),_:1}),U(Il,{prop:"publisher",align:"center",label:"出版社","show-overflow-tooltip":""},{default:T((({row:l})=>[I("div",Fe,[U(va,{content:l.publisher||"-",placement:"top",disabled:!ba(e.$event),enterable:!1},{default:T((()=>[I("span",Le,M(l.publisher||"-"),1)])),_:2},1032,["content","disabled"])],512)])),_:1}),U(Il,{prop:"publicationTime",align:"center",label:"出版时间",width:"90"},{default:T((({row:e})=>[j(M(e.publicationTime||"-"),1)])),_:1}),U(Il,{prop:"bindingLayout",align:"center",label:"装帧",width:"80"},{default:T((({row:e})=>[j(M(e.bindingLayout||"-"),1)])),_:1}),U(Il,{prop:"fixPrice",align:"center",label:"定价",width:"80"},{default:T((({row:e})=>[j(M(e.fixPrice?(e.fixPrice/100).toFixed(2):"-"),1)])),_:1}),U(Il,{align:"center",label:"全部销量",width:"90"},{default:T((({row:e})=>[j(M(e.totalSale||0),1)])),_:1}),U(Il,{align:"center",label:"销量详情",width:"100"},{default:T((({row:e})=>[U(fa,{placement:"right",trigger:"hover",width:220,"popper-class":"sales-popover"},{default:T((()=>[I("div",Oe,[I("div",qe,[$e,j(" "+M(e.daySale7||0),1)]),I("div",Ee,[Je,j(" "+M(e.daySale15||0),1)]),I("div",We,[Ge,j(" "+M(e.daySale30||0),1)]),I("div",He,[Ze,j(" "+M(e.daySale60||0),1)]),I("div",Ke,[Qe,j(" "+M(e.daySale90||0),1)]),I("div",Xe,[el,j(" "+M(e.daySale180||0),1)]),I("div",ll,[al,j(" "+M(e.daySale365||0),1)]),I("div",tl,[ol,j(" "+M(e.thisYearSale||0),1)]),I("div",sl,[il,j(" "+M(e.lastYearSale||0),1)]),I("div",nl,[rl,j(" "+M(e.totalSale||0),1)])])])),reference:T((()=>[I("div",dl,[I("span",null,M(e.sale7Days||0),1),U(Z,null,{default:T((()=>[U(C(D))])),_:1})])])),_:2},1024)])),_:1}),U(Il,{align:"center",label:"在售",width:"80"},{default:T((({row:e})=>[j(M(e.sellCount||0),1)])),_:1}),U(Il,{align:"center",label:"违规类型",width:"100"},{default:T((({row:e})=>[ca(e)?(S(),A(va,{key:0,placement:"top",content:pa(e)},{default:T((()=>[U(ga,{type:"danger",size:"small",effect:"plain"},{default:T((()=>[j("违规")])),_:1})])),_:2},1032,["content"])):(S(),P("span",ul,"正常"))])),_:1}),U(Il,{align:"center",label:"操作",width:"100"},{default:T((({row:e})=>[U(Q,{type:"primary",size:"small",text:"",onClick:l=>(e=>{ua(),Ml.value="edit";const l=JSON.parse(JSON.stringify(e));l.fixPrice&&(l.fixPrice=l.fixPrice/100),Object.keys(Ol).forEach((e=>{e in l&&(Ol[e]=l[e])})),zl.value=!0})(e)},{default:T((()=>[j("编辑")])),_:2},1032,["onClick"]),U(Q,{type:"danger",size:"small",text:"",onClick:l=>(e=>{$.confirm("确定要删除该记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await K(e.id),t.success("删除成功"),Hl()}catch(l){console.error("删除失败:",l),t.error("删除失败")}})).catch((()=>{}))})(e)},{default:T((()=>[j("删除")])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Ua,J.value]]),I("div",cl,[U(ka,{"current-page":Bl.current,"page-size":Bl.size,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:Bl.total,onSizeChange:Kl,onCurrentChange:Ql},null,8,["current-page","page-size","total"])]),U(Pa,{title:"add"===Ml.value?"新增图书信息":"编辑图书信息",modelValue:zl.value,"onUpdate:modelValue":l[25]||(l[25]=e=>zl.value=e),width:"800px","close-on-click-modal":!1},{footer:T((()=>[I("span",bl,[U(Q,{onClick:l[24]||(l[24]=e=>zl.value=!1)},{default:T((()=>[j("取消")])),_:1}),U(Q,{type:"primary",onClick:da,loading:Yl.value},{default:T((()=>[j("确定")])),_:1},8,["loading"])])])),default:T((()=>[U(Sa,{ref_key:"formRef",ref:Rl,model:Ol,rules:ql,"label-width":"120px","label-position":"right"},{default:T((()=>[U(xa,{gutter:20},{default:T((()=>[U(Va,{span:12},{default:T((()=>[U(_a,{label:"书名",prop:"bookName"},{default:T((()=>[U(a,{modelValue:Ol.bookName,"onUpdate:modelValue":l[12]||(l[12]=e=>Ol.bookName=e),placeholder:"请输入书名",maxlength:"400","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1}),U(Va,{span:12},{default:T((()=>[U(_a,{label:"ISBN",prop:"isbn"},{default:T((()=>[U(a,{modelValue:Ol.isbn,"onUpdate:modelValue":l[13]||(l[13]=e=>Ol.isbn=e),placeholder:"请输入ISBN",maxlength:"30",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1})])),_:1})])),_:1}),U(xa,{gutter:20},{default:T((()=>[U(Va,{span:12},{default:T((()=>[U(_a,{label:"作者",prop:"author"},{default:T((()=>[U(a,{modelValue:Ol.author,"onUpdate:modelValue":l[14]||(l[14]=e=>Ol.author=e),placeholder:"请输入作者",maxlength:"400",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1})])),_:1}),U(Va,{span:12},{default:T((()=>[U(_a,{label:"出版社",prop:"publisher"},{default:T((()=>[U(a,{modelValue:Ol.publisher,"onUpdate:modelValue":l[15]||(l[15]=e=>Ol.publisher=e),placeholder:"请输入出版社",maxlength:"200",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1})])),_:1})])),_:1}),U(xa,{gutter:20},{default:T((()=>[U(Va,{span:12},{default:T((()=>[U(_a,{label:"出版时间",prop:"publicationTime"},{default:T((()=>[U(H,{modelValue:Ol.publicationTime,"onUpdate:modelValue":l[16]||(l[16]=e=>Ol.publicationTime=e),type:"date",placeholder:"选择出版时间","value-format":"YYYY-MM",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1})])),_:1}),U(Va,{span:12},{default:T((()=>[U(_a,{label:"装帧",prop:"bindingLayout"},{default:T((()=>[U(a,{modelValue:Ol.bindingLayout,"onUpdate:modelValue":l[17]||(l[17]=e=>Ol.bindingLayout=e),placeholder:"请输入装帧",maxlength:"30",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1})])),_:1})])),_:1}),U(xa,{gutter:20},{default:T((()=>[U(Va,{span:12},{default:T((()=>[U(_a,{label:"定价",prop:"fixPrice"},{default:T((()=>[U(X,{modelValue:Ol.fixPrice,"onUpdate:modelValue":l[18]||(l[18]=e=>Ol.fixPrice=e),min:0,precision:2,step:1,placeholder:"请输入定价",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1})])),_:1}),U(Va,{span:12},{default:T((()=>[U(_a,{label:"页数",prop:"pages"},{default:T((()=>[U(X,{modelValue:Ol.pages,"onUpdate:modelValue":l[19]||(l[19]=e=>Ol.pages=e),min:0,precision:0,step:1,placeholder:"请输入页数",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1})])),_:1})])),_:1}),U(xa,{gutter:20},{default:T((()=>[U(Va,{span:12},{default:T((()=>[U(_a,{label:"编辑",prop:"editor"},{default:T((()=>[U(a,{modelValue:Ol.editor,"onUpdate:modelValue":l[20]||(l[20]=e=>Ol.editor=e),placeholder:"请输入编辑",maxlength:"30",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1})])),_:1}),U(Va,{span:12},{default:T((()=>[U(_a,{label:"版次",prop:"edition"},{default:T((()=>[U(a,{modelValue:Ol.edition,"onUpdate:modelValue":l[21]||(l[21]=e=>Ol.edition=e),placeholder:"请输入版次",maxlength:"50",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1})])),_:1})])),_:1}),U(_a,{label:"书图片",prop:"bookPic"},{default:T((()=>[U(wa,{class:"book-pic-uploader",action:Ll.value,"show-file-list":!1,"on-success":ra,"before-upload":na,disabled:"edit"===Ml.value},{default:T((()=>[Ol.bookPic&&"0"!==Ol.bookPic?(S(),P("img",{key:0,src:Ol.bookPic,class:"book-pic"},null,8,pl)):(S(),A(Z,{key:1,class:B(["book-pic-uploader-icon",{"disabled-uploader":"edit"===Ml.value}])},{default:T((()=>[U(C(F))])),_:1},8,["class"]))])),_:1},8,["action","disabled"]),"edit"===Ml.value?(S(),P("div",ml,"编辑模式下不允许更改图片")):L("",!0)])),_:1}),U(_a,{label:"内容简介",prop:"content"},{default:T((()=>[U(a,{modelValue:Ol.content,"onUpdate:modelValue":l[22]||(l[22]=e=>Ol.content=e),type:"textarea",rows:4,placeholder:"请输入内容简介",maxlength:"500","show-word-limit":"",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1}),U(_a,{label:"备注",prop:"remark"},{default:T((()=>[U(a,{modelValue:Ol.remark,"onUpdate:modelValue":l[23]||(l[23]=e=>Ol.remark=e),type:"textarea",rows:3,placeholder:"请输入备注",maxlength:"500","show-word-limit":"",disabled:"edit"===Ml.value},null,8,["modelValue","disabled"])])),_:1})])),_:1},8,["model"])])),_:1},8,["title","modelValue"]),U(Pa,{title:"设置违规类型",modelValue:Al.value,"onUpdate:modelValue":l[32]||(l[32]=e=>Al.value=e),width:"500px","close-on-click-modal":!1,class:"violation-dialog"},{footer:T((()=>[I("span",Pl,[U(Q,{onClick:l[31]||(l[31]=e=>Al.value=!1)},{default:T((()=>[j("取消")])),_:1}),U(Q,{type:"primary",onClick:oa,loading:Dl.value},{default:T((()=>[j("确定")])),_:1},8,["loading"])])])),default:T((()=>[I("div",vl,[U(xa,{gutter:20},{default:T((()=>[U(Va,{span:12},{default:T((()=>[I("div",hl,[U(Ia,{modelValue:Fl.vioBook,"onUpdate:modelValue":l[26]||(l[26]=e=>Fl.vioBook=e)},{default:T((()=>[fl])),_:1},8,["modelValue"])])])),_:1}),U(Va,{span:12},{default:T((()=>[I("div",gl,[U(Ia,{modelValue:Fl.bookSet,"onUpdate:modelValue":l[27]||(l[27]=e=>Fl.bookSet=e)},{default:T((()=>[yl])),_:1},8,["modelValue"])])])),_:1})])),_:1}),U(xa,{gutter:20},{default:T((()=>[U(Va,{span:12},{default:T((()=>[I("div",kl,[U(Ia,{modelValue:Fl.onenumMbooks,"onUpdate:modelValue":l[28]||(l[28]=e=>Fl.onenumMbooks=e)},{default:T((()=>[_l])),_:1},8,["modelValue"])])])),_:1}),U(Va,{span:12},{default:T((()=>[I("div",Vl,[U(Ia,{modelValue:Fl.illPublisher,"onUpdate:modelValue":l[29]||(l[29]=e=>Fl.illPublisher=e)},{default:T((()=>[xl])),_:1},8,["modelValue"])])])),_:1})])),_:1}),U(xa,{gutter:20},{default:T((()=>[U(Va,{span:12},{default:T((()=>[I("div",wl,[U(Ia,{modelValue:Fl.illAuthor,"onUpdate:modelValue":l[30]||(l[30]=e=>Fl.illAuthor=e)},{default:T((()=>[Sl])),_:1},8,["modelValue"])])])),_:1})])),_:1})])])),_:1},8,["modelValue"])])}},__scopeId:"data-v-1a0e5572"};export{Il as default};
