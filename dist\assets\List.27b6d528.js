import{Z as e,a8 as a,_ as t,ao as l,a9 as r,aa as o,aj as s,ar as n,ah as i,ai as d,a3 as c,ab as u,ac as m,ae as p,af as f,ag as v,am as g,as as y,o as h,k as T,l as b,m as w,w as x,v as _,at as V,F as D,t as C,a6 as R,a7 as k,E as j}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                   *//* empty css                      *//* empty css                    *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                     *//* empty css                       *//* empty css                        */import{c as z}from"./cards.9e024648.js";import"./RefreshButton.c2d64dc7.js";import{A as S}from"./ActionBar.acef8039.js";import"./index.9f8b8a79.js";const U=e=>(R("data-v-545501ee"),e=e(),k(),e),B={class:"list-container"},E={class:"search-area"},$={class:"pagination-container"},I={class:"card-secret-content"},N=U((()=>b("p",null,"您的卡密为：",-1))),O={class:"dialog-footer"},Y={class:"card-params-content"},A=U((()=>b("span",{class:"unit-label"},"天",-1))),H={class:"dialog-footer"},M={__name:"List",setup(R){const k=e([]),U=e(!1),M=e(null),P=a({cardId:"",cardSecret:"",status:"",effectiveDays:0,activateTimeRange:[],expireTimeRange:[]}),L=a({current:1,size:10,total:0}),q=e(!1),F=e(""),Q=e(!1),Z=a({effectiveDays:30,memo:""}),G=[{text:"最近一周",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-6048e5),[a,e]}},{text:"最近一个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-2592e6),[a,e]}},{text:"最近三个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-7776e6),[a,e]}},{text:"最近半年",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-15552e6),[a,e]}},{text:"最近一年",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-31536e6),[a,e]}}];t((()=>{J()}));const J=async()=>{U.value=!0;try{const e={page:L.current,size:L.size,cardId:P.cardId||void 0,cardSecret:P.cardSecret||void 0,status:null!==P.status?P.status:void 0,effectiveDays:P.effectiveDays||void 0};P.activateTimeRange&&2===P.activateTimeRange.length&&(e.activateTimeStart=P.activateTimeRange[0],e.activateTimeEnd=P.activateTimeRange[1]),P.expireTimeRange&&2===P.expireTimeRange.length&&(e.expireTimeStart=P.expireTimeRange[0],e.expireTimeEnd=P.expireTimeRange[1]);const a=await z.pageQueryCard(e);200===a.code?(k.value=a.data.list||[],L.total=a.data.total||0):l.error(a.message||"获取数据失败")}catch(e){console.error("获取数据失败:",e),"ECONNABORTED"===e.code?l.error("请求超时，请检查网络连接或联系管理员"):e.response?l.error(`请求失败: ${e.response.status} ${e.response.statusText}`):e.request?l.error("服务器未响应，请稍后再试"):l.error(`请求错误: ${e.message}`),k.value=[],L.total=0}finally{U.value=!1}},K=()=>{Q.value=!0},W=async()=>{try{const e={cardType:"verifyPriceCredential",effectiveDays:Z.effectiveDays,memo:Z.memo},a=await z.createCardSecret(e);console.log("res",a),200===a.code?(F.value=a.data,Q.value=!1,q.value=!0):l.error(a.message||"获取卡密失败")}catch(e){console.error("获取卡密失败:",e),l.error("获取卡密失败: "+(e.response.data.message||"未知错误"))}},X=()=>{try{const e=document.createElement("textarea");e.value=F.value,e.style.position="fixed",e.style.opacity="0",document.body.appendChild(e),e.select(),console.log("textarea",e);const a=document.execCommand("copy");document.body.removeChild(e),a?l.success("复制成功"):l.warning("复制失败，请手动复制"),q.value=!1}catch(e){console.error("复制失败:",e),l.error("复制失败，请手动复制")}},ee=()=>{J()},ae=()=>{L.current=1,J()},te=()=>{P.cardId="",P.cardSecret="",P.status=null,P.effectiveDays=0,P.activateTimeRange=[],P.expireTimeRange=[],L.current=1,J()},le=e=>{L.size=e,L.current=1,J()},re=e=>{L.current=e,J()},oe=e=>({0:"未激活",1:"未使用",2:"已使用",3:"已冻结",4:"已过期"}[e]||"未知"),se=e=>{if(!e)return"-";return new Date(e).toLocaleString()};return(e,a)=>{const t=r,R=o,ne=s,ie=n,de=i,ce=d,ue=c,me=u,pe=m,fe=p,ve=f,ge=v,ye=g,he=y;return h(),T("div",B,[b("div",E,[w(me,{inline:!0,model:P},{default:x((()=>[w(R,{label:"卡密账号"},{default:x((()=>[w(t,{modelValue:P.cardId,"onUpdate:modelValue":a[0]||(a[0]=e=>P.cardId=e),placeholder:"请输入卡密账号",clearable:""},null,8,["modelValue"])])),_:1}),w(R,{label:"卡密密码"},{default:x((()=>[w(t,{modelValue:P.cardSecret,"onUpdate:modelValue":a[1]||(a[1]=e=>P.cardSecret=e),placeholder:"请输入卡密密码",clearable:""},null,8,["modelValue"])])),_:1}),w(R,{label:"有效期"},{default:x((()=>[w(ne,{modelValue:P.effectiveDays,"onUpdate:modelValue":a[2]||(a[2]=e=>P.effectiveDays=e),placeholder:"请输入有效期天数",min:0,clearable:""},null,8,["modelValue"])])),_:1}),w(R,{label:"激活时间"},{default:x((()=>[w(ie,{modelValue:P.activateTimeRange,"onUpdate:modelValue":a[3]||(a[3]=e=>P.activateTimeRange=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期时间","end-placeholder":"结束日期时间","value-format":"YYYY-MM-DD HH:mm:ss",shortcuts:G},null,8,["modelValue"])])),_:1}),w(R,{label:"过期时间"},{default:x((()=>[w(ie,{modelValue:P.expireTimeRange,"onUpdate:modelValue":a[4]||(a[4]=e=>P.expireTimeRange=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期时间","end-placeholder":"结束日期时间","value-format":"YYYY-MM-DD HH:mm:ss",shortcuts:G},null,8,["modelValue"])])),_:1}),w(R,{label:"状态"},{default:x((()=>[w(ce,{modelValue:P.status,"onUpdate:modelValue":a[5]||(a[5]=e=>P.status=e),placeholder:"请选择状态",clearable:"",style:{"min-width":"100px"}},{default:x((()=>[w(de,{label:"未激活",value:0}),w(de,{label:"未使用",value:1}),w(de,{label:"已使用",value:2}),w(de,{label:"已冻结",value:3}),w(de,{label:"已过期",value:4})])),_:1},8,["modelValue"])])),_:1}),w(R,null,{default:x((()=>[w(ue,{type:"primary",onClick:ae},{default:x((()=>[_("搜索")])),_:1}),w(ue,{onClick:te},{default:x((()=>[_("重置")])),_:1})])),_:1})])),_:1},8,["model"])]),w(S,{onRefresh:ee},{left:x((()=>[w(ue,{type:"primary",onClick:K},{default:x((()=>[_("获取卡密")])),_:1})])),_:1}),V((h(),D(ve,{ref_key:"tableRef",ref:M,data:k.value,border:"",stripe:"",style:{width:"100%"}},{default:x((()=>[w(pe,{type:"selection",align:"center",width:"55"}),w(pe,{prop:"cardId",label:"卡密账号","min-width":"180"}),w(pe,{prop:"cardSecret",label:"卡密密码","min-width":"180"}),w(pe,{prop:"cardType",label:"卡密类型","min-width":"120"},{default:x((({row:e})=>{return[_(C((a=e.cardType,{verifyPriceCredential:"使用核价凭证"}[a]||a)),1)];var a})),_:1}),w(pe,{prop:"status",label:"状态",width:"100"},{default:x((({row:e})=>{return[w(fe,{type:(a=e.status,{0:"info",1:"success",2:"warning",3:"danger",4:"info"}[a]||"info")},{default:x((()=>[_(C(oe(e.status)),1)])),_:2},1032,["type"])];var a})),_:1}),w(pe,{prop:"faceValue",label:"面值",width:"100"},{default:x((({row:e})=>[_(C(e.faceValue)+"元 ",1)])),_:1}),w(pe,{prop:"balance",label:"当前余额",width:"100"},{default:x((({row:e})=>[_(C(e.balance)+"元 ",1)])),_:1}),w(pe,{prop:"effectiveDays",label:"有效期",width:"100"},{default:x((({row:e})=>[_(C(e.effectiveDays)+"天 ",1)])),_:1}),w(pe,{prop:"activateTime",label:"激活时间","min-width":"160"},{default:x((({row:e})=>[_(C(se(e.activateTime)),1)])),_:1}),w(pe,{prop:"expireTime",label:"过期时间","min-width":"160"},{default:x((({row:e})=>[_(C(se(e.expireTime)),1)])),_:1}),w(pe,{prop:"useTime",label:"使用时间","min-width":"160"},{default:x((({row:e})=>[_(C(se(e.useTime)),1)])),_:1}),w(pe,{prop:"memo",label:"备注","min-width":"120"}),w(pe,{label:"操作",fixed:"right",width:"180"},{default:x((({row:e})=>[w(ue,{size:"small",type:"danger",onClick:a=>{return t=e.id,void j.confirm("确定要删除该卡密吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=await z.deleteCard(t);200===e.code?(l.success("删除成功"),J()):l.error(e.message||"删除失败")}catch(e){console.error("删除失败:",e),"ECONNABORTED"===e.code?l.error("请求超时，请稍后再试"):e.response?l.error(`删除失败: ${e.response.status} ${e.response.statusText}`):l.error(`删除失败: ${e.message||"未知错误"}`)}})).catch((()=>{}));var t},disabled:3===e.status||4===e.status},{default:x((()=>[_("删除")])),_:2},1032,["onClick","disabled"]),3!==e.status?(h(),D(ue,{key:0,size:"small",type:"warning",onClick:a=>{return t=e.id,void j.confirm("确定要停用该卡密吗？停用后将无法使用","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=await z.disableCard(t);200===e.code?(l.success("停用成功"),J()):l.error(e.message||"停用失败")}catch(e){console.error("停用失败:",e),"ECONNABORTED"===e.code?l.error("请求超时，请稍后再试"):e.response?l.error(`停用失败: ${e.response.status} ${e.response.statusText}`):l.error(`停用失败: ${e.message||"未知错误"}`)}})).catch((()=>{}));var t},disabled:4===e.status||0===e.status},{default:x((()=>[_("停用")])),_:2},1032,["onClick","disabled"])):(h(),D(ue,{key:1,size:"small",type:"success",onClick:a=>{return t=e.id,void j.confirm("确定要启用该卡密吗？启用后将可以使用","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=await z.enableCard(t);200===e.code?(l.success("启用成功"),J()):l.error(e.message||"启用失败")}catch(e){console.error("启用失败:",e),"ECONNABORTED"===e.code?l.error("请求超时，请稍后再试"):e.response?l.error(`启用失败: ${e.response.status} ${e.response.statusText}`):l.error(`启用失败: ${e.message||"未知错误"}`)}})).catch((()=>{}));var t}},{default:x((()=>[_("启用")])),_:2},1032,["onClick"]))])),_:1})])),_:1},8,["data"])),[[he,U.value]]),b("div",$,[w(ge,{"current-page":L.current,"onUpdate:currentPage":a[6]||(a[6]=e=>L.current=e),"page-size":L.size,"onUpdate:pageSize":a[7]||(a[7]=e=>L.size=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:L.total,onSizeChange:le,onCurrentChange:re},null,8,["current-page","page-size","total"])]),w(ye,{title:"卡密信息",modelValue:q.value,"onUpdate:modelValue":a[10]||(a[10]=e=>q.value=e),width:"500px","close-on-click-modal":!1},{footer:x((()=>[b("span",O,[w(ue,{onClick:a[9]||(a[9]=e=>q.value=!1)},{default:x((()=>[_("关闭")])),_:1}),w(ue,{type:"primary",onClick:X},{default:x((()=>[_("复制")])),_:1})])])),default:x((()=>[b("div",I,[N,w(t,{modelValue:F.value,"onUpdate:modelValue":a[8]||(a[8]=e=>F.value=e),type:"textarea",rows:3,readonly:""},null,8,["modelValue"])])])),_:1},8,["modelValue"]),w(ye,{title:"添加卡密参数",modelValue:Q.value,"onUpdate:modelValue":a[14]||(a[14]=e=>Q.value=e),width:"500px","close-on-click-modal":!1},{footer:x((()=>[b("span",H,[w(ue,{onClick:a[13]||(a[13]=e=>Q.value=!1)},{default:x((()=>[_("取消")])),_:1}),w(ue,{type:"primary",onClick:W},{default:x((()=>[_("提交")])),_:1})])])),default:x((()=>[b("div",Y,[w(me,{model:Z,"label-width":"80px"},{default:x((()=>[w(R,{label:"有效期"},{default:x((()=>[w(ne,{modelValue:Z.effectiveDays,"onUpdate:modelValue":a[11]||(a[11]=e=>Z.effectiveDays=e),min:1,max:365},null,8,["modelValue"]),A])),_:1}),w(R,{label:"备注"},{default:x((()=>[w(t,{modelValue:Z.memo,"onUpdate:modelValue":a[12]||(a[12]=e=>Z.memo=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1},8,["modelValue"])])}},__scopeId:"data-v-545501ee"};export{M as default};
