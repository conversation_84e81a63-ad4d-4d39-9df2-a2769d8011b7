import{T as e,Z as a,_ as l,a9 as s,aa as r,a3 as o,ab as d,o as u,k as n,l as i,m as t,w as p,n as c,r as m,aX as v,aY as g,q as f,F as h,aZ as w,an as y,v as b,a6 as k,a7 as V,ao as T}from"./vendor.9a6f3141.js";/* empty css                     *//* empty css                 */const x=e=>(k("data-v-2d509388"),e=e(),V(),e),I={class:"register-page"},_={class:"register-container"},N={class:"register-form"},C=x((()=>i("div",{class:"form-header"},[i("h2",null,"与书有行"),i("div",{class:"language-switch"},[i("span",null,"A")])],-1))),M=x((()=>i("div",{class:"input-tip"},"请输入手机号",-1))),U=x((()=>i("div",{class:"input-tip"},"请输入密码密码",-1))),z={class:"captcha-row"},j=["src"],q=x((()=>i("div",{class:"login-link"},[i("span",null,"使用已有账户登录")],-1))),S={__name:"index",setup(k){const V=e(),x=a(!1),S=a(),P=a(""),E=a(!0),R=a({pddMallId:"",pddMallName:"",type:"",accessToken:"",skuSpec:""}),O=a({username:"",phoneNumber:"",password:"",confirmPassword:"",inviteCode:"",code:"",uuid:"",clientId:"e5cd7e4891bf95d1d19206ce24a7b32e",grantType:"password",tenantId:"000000",userType:"sys_user"}),Z={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:2,max:20,message:"用户名长度在2到20个字符",trigger:"blur"}],phoneNumber:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:5,max:20,message:"密码长度在5到20个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(e,a,l)=>{a!==O.value.password?l(new Error("两次输入密码不一致")):l()},trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur",validator:(e,a,l)=>{E.value?a?l():l(new Error("请输入验证码")):l()}}]},A=async()=>{try{const e=await fetch("/auth/code",{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok){const a=await e.json();if(200===a.code){const{data:e}=a;E.value=void 0===e.captchaEnabled||e.captchaEnabled,E.value?(O.value.uuid=e.uuid,P.value="data:image/gif;base64,"+e.img):(O.value.uuid="",P.value="",console.log("验证码功能未启用"))}else T.error(a.msg||"获取验证码失败")}else T.error("获取验证码失败，请重试")}catch(e){console.error("获取验证码失败:",e),T.error("网络错误，获取验证码失败")}},F=async()=>{try{if(!(await S.value.validate()))return;x.value=!0;const e={username:O.value.username,password:O.value.password,phoneNumber:O.value.phoneNumber,inviteCode:O.value.inviteCode,clientId:O.value.clientId,grantType:O.value.grantType,tenantId:O.value.tenantId,userType:O.value.userType,pddMallId:R.value.pddMallId,pddMallName:R.value.pddMallName,pddType:R.value.type,accessToken:R.value.accessToken,skuSpec:R.value.skuSpec};E.value&&(e.code=O.value.code,e.uuid=O.value.uuid),console.log("注册数据:",e);const a=await fetch("/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),l=await a.json();200===l.code?T.success("注册成功！请复制链接到https://erp.buzhiyushu.cn/登录"):(T.error(l.msg||"注册失败，请重试"),E.value&&l.msg&&(l.msg.includes("验证码")||l.msg.includes("captcha"))&&(A(),O.value.code=""))}catch(e){console.error("注册失败:",e),T.error("注册失败，请重试")}finally{x.value=!1}};return l((()=>{(()=>{const e=V.query;R.value={pddMallId:e.pddMallId||"",pddMallName:decodeURIComponent(e.pddMallName||""),type:e.type||"",accessToken:e.accessToken||"",skuSpec:decodeURIComponent(e.skuSpec||"")},R.value.pddMallId&&(O.value.username="pdd"+R.value.pddMallId),console.log("获取到的URL参数:",R.value)})(),A()})),(e,a)=>{const l=s,k=r,V=o,T=d;return u(),n("div",I,[i("div",_,[i("div",N,[C,t(T,{ref_key:"formRef",ref:S,model:O.value,rules:Z,"label-width":"0"},{default:p((()=>[t(k,{prop:"username"},{default:p((()=>[t(l,{modelValue:O.value.username,"onUpdate:modelValue":a[0]||(a[0]=e=>O.value.username=e),placeholder:"用户名",size:"large","prefix-icon":c(m)},null,8,["modelValue","prefix-icon"])])),_:1}),t(k,{prop:"phoneNumber"},{default:p((()=>[t(l,{modelValue:O.value.phoneNumber,"onUpdate:modelValue":a[1]||(a[1]=e=>O.value.phoneNumber=e),placeholder:"手机号",size:"large","prefix-icon":c(v)},null,8,["modelValue","prefix-icon"]),M])),_:1}),t(k,{prop:"password"},{default:p((()=>[t(l,{modelValue:O.value.password,"onUpdate:modelValue":a[2]||(a[2]=e=>O.value.password=e),type:"password",placeholder:"密码",size:"large","prefix-icon":c(g),"show-password":""},null,8,["modelValue","prefix-icon"]),U])),_:1}),t(k,{prop:"confirmPassword"},{default:p((()=>[t(l,{modelValue:O.value.confirmPassword,"onUpdate:modelValue":a[3]||(a[3]=e=>O.value.confirmPassword=e),type:"password",placeholder:"确认密码",size:"large","prefix-icon":c(g),"show-password":""},null,8,["modelValue","prefix-icon"])])),_:1}),t(k,{prop:"inviteCode"},{default:p((()=>[t(l,{modelValue:O.value.inviteCode,"onUpdate:modelValue":a[4]||(a[4]=e=>O.value.inviteCode=e),placeholder:"邀请码",size:"large","prefix-icon":c(f)},null,8,["modelValue","prefix-icon"])])),_:1}),E.value?(u(),h(k,{key:0,prop:"code"},{default:p((()=>[i("div",z,[t(l,{modelValue:O.value.code,"onUpdate:modelValue":a[5]||(a[5]=e=>O.value.code=e),placeholder:"验证码",size:"large","prefix-icon":c(w)},null,8,["modelValue","prefix-icon"]),i("div",{class:"captcha-image",onClick:A},[P.value?(u(),n("img",{key:0,src:P.value,alt:"验证码"},null,8,j)):y("",!0)])])])),_:1})):y("",!0),t(k,null,{default:p((()=>[t(V,{type:"primary",size:"large",class:"register-btn",onClick:F,loading:x.value},{default:p((()=>[b(" 注册 ")])),_:1},8,["loading"])])),_:1}),q])),_:1},8,["model"])])])])}},__scopeId:"data-v-2d509388"};export{S as default};
