import{T as e,Z as a,_ as l,a9 as r,aa as s,a3 as o,ab as d,o as u,k as n,l as t,m as i,w as c,n as p,r as m,aX as v,aY as g,q as f,F as h,aZ as y,an as w,v as b,a6 as T,a7 as x,ao as k}from"./vendor.9a6f3141.js";/* empty css                     *//* empty css                 */const I=e=>(T("data-v-58e4c009"),e=e(),x(),e),V={class:"register-page"},_={class:"register-container"},N={class:"register-form"},C=I((()=>t("div",{class:"form-header"},[t("h2",null,"与书同行")],-1))),M={class:"captcha-row"},S=["src"],z=I((()=>t("div",{class:"login-link"},[t("span",null,"使用已有账户登录")],-1))),P={__name:"index",setup(T){const x=e(),I=a(!1),P=a(),U=a(""),j=a(!0),q=a({pddMallId:"",pddMallName:"",type:"",accessToken:"",skuSpec:""}),E=a({username:"",phoneNumber:"",password:"",confirmPassword:"",inviteCode:"",code:"",uuid:"",clientId:"e5cd7e4891bf95d1d19206ce24a7b32e",grantType:"password",tenantId:"000000",userType:"sys_user"}),$={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:2,max:20,message:"用户名长度在2到20个字符",trigger:"blur"}],phoneNumber:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:5,max:20,message:"密码长度在5到20个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(e,a,l)=>{a!==E.value.password?l(new Error("两次输入密码不一致")):l()},trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur",validator:(e,a,l)=>{j.value?a?l():l(new Error("请输入验证码")):l()}}]},O=async()=>{try{const e=await fetch("/auth/code",{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok){const a=e.headers.get("content-type");if(a&&a.includes("application/json")){const a=await e.json();if(200===a.code){const{data:e}=a;j.value=void 0===e.captchaEnabled||e.captchaEnabled,j.value?(E.value.uuid=e.uuid,U.value="data:image/gif;base64,"+e.img):(E.value.uuid="",U.value="",console.log("验证码功能未启用"))}else k.error(a.msg||"获取验证码失败")}else{const a=await e.text();console.error("服务器返回非JSON响应:",a),k.error("服务器响应格式错误，请检查API接口")}}else console.error(`HTTP错误: ${e.status} ${e.statusText}`),k.error(`获取验证码失败 (${e.status})，请重试`)}catch(e){console.error("获取验证码失败:",e),"SyntaxError"===e.name&&e.message.includes("JSON")?k.error("服务器响应格式错误，请检查API接口"):k.error("网络错误，获取验证码失败")}},R=async()=>{try{if(!(await P.value.validate()))return;I.value=!0;const a={username:E.value.username,password:E.value.password,phoneNumber:E.value.phoneNumber,inviteCode:E.value.inviteCode,clientId:E.value.clientId,grantType:E.value.grantType,tenantId:E.value.tenantId,userType:E.value.userType,pddMallId:q.value.pddMallId,pddMallName:q.value.pddMallName,pddType:q.value.type,accessToken:q.value.accessToken,skuSpec:q.value.skuSpec};j.value&&(a.code=E.value.code,a.uuid=E.value.uuid),console.log("注册数据:",a);const l=await fetch("/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),r=await l.json();if(200===r.code){const a="https://erp.buzhiyushu.cn/";try{await navigator.clipboard.writeText(a),k.success("注册成功！登录链接https://erp.buzhiyushu.cn/已复制到剪贴板")}catch(e){k.success(`注册成功！请复制链接：${a}`)}}else k.error(r.msg||"注册失败，请重试"),j.value&&r.msg&&(r.msg.includes("验证码")||r.msg.includes("captcha"))&&(O(),E.value.code="")}catch(a){console.error("注册失败:",a),k.error("注册失败，请重试")}finally{I.value=!1}};return l((()=>{(()=>{const e=x.query;q.value={pddMallId:e.pddMallId||"",pddMallName:decodeURIComponent(e.pddMallName||""),type:e.type||"",accessToken:e.accessToken||"",skuSpec:decodeURIComponent(e.skuSpec||"")},q.value.pddMallId&&(E.value.username="pdd"+q.value.pddMallId),console.log("获取到的URL参数:",q.value)})(),O()})),(e,a)=>{const l=r,T=s,x=o,k=d;return u(),n("div",V,[t("div",_,[t("div",N,[C,i(k,{ref_key:"formRef",ref:P,model:E.value,rules:$,"label-width":"0"},{default:c((()=>[i(T,{prop:"username"},{default:c((()=>[i(l,{modelValue:E.value.username,"onUpdate:modelValue":a[0]||(a[0]=e=>E.value.username=e),placeholder:"用户名",size:"large","prefix-icon":p(m)},null,8,["modelValue","prefix-icon"])])),_:1}),i(T,{prop:"phoneNumber"},{default:c((()=>[i(l,{modelValue:E.value.phoneNumber,"onUpdate:modelValue":a[1]||(a[1]=e=>E.value.phoneNumber=e),placeholder:"手机号",size:"large","prefix-icon":p(v)},null,8,["modelValue","prefix-icon"])])),_:1}),i(T,{prop:"password"},{default:c((()=>[i(l,{modelValue:E.value.password,"onUpdate:modelValue":a[2]||(a[2]=e=>E.value.password=e),type:"password",placeholder:"密码",size:"large","prefix-icon":p(g),"show-password":""},null,8,["modelValue","prefix-icon"])])),_:1}),i(T,{prop:"confirmPassword"},{default:c((()=>[i(l,{modelValue:E.value.confirmPassword,"onUpdate:modelValue":a[3]||(a[3]=e=>E.value.confirmPassword=e),type:"password",placeholder:"确认密码",size:"large","prefix-icon":p(g),"show-password":""},null,8,["modelValue","prefix-icon"])])),_:1}),i(T,{prop:"inviteCode"},{default:c((()=>[i(l,{modelValue:E.value.inviteCode,"onUpdate:modelValue":a[4]||(a[4]=e=>E.value.inviteCode=e),placeholder:"邀请码",size:"large","prefix-icon":p(f)},null,8,["modelValue","prefix-icon"])])),_:1}),j.value?(u(),h(T,{key:0,prop:"code"},{default:c((()=>[t("div",M,[i(l,{modelValue:E.value.code,"onUpdate:modelValue":a[5]||(a[5]=e=>E.value.code=e),placeholder:"验证码",size:"large","prefix-icon":p(y)},null,8,["modelValue","prefix-icon"]),t("div",{class:"captcha-image",onClick:O},[U.value?(u(),n("img",{key:0,src:U.value,alt:"验证码"},null,8,S)):w("",!0)])])])),_:1})):w("",!0),i(T,null,{default:c((()=>[i(x,{type:"primary",size:"large",class:"register-btn",onClick:R,loading:I.value},{default:c((()=>[b(" 注册 ")])),_:1},8,["loading"])])),_:1}),z])),_:1},8,["model"])])])])}},__scopeId:"data-v-58e4c009"};export{P as default};
