import{Z as e,a8 as a,_ as l,ao as o,ax as r,ay as s,o as t,k as i,l as d,m as n,w as u,v as c,at as m,F as p,t as f,a6 as v,a7 as b,a3 as y,ac as g,ae as h,af as C,a5 as V,a9 as _,aa as w,al as k,ab as F,am as R,as as j}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                      *//* empty css                    *//* empty css                   *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                *//* empty css                        *//* empty css                   */import{g as D,u as N,a as x,d as U,b as E,c as P}from"./role.579a6f19.js";import{g as T}from"./index.9f8b8a79.js";const z={name:"RoleManage",setup(){const r=e(!1),s=e([]),t=e(null),i=e(!1),d=e(!1),n=a({id:"",roleName:"",roleCode:"",description:"",status:1}),u=e(null),c=e([]),m=e(!1),p=e(""),f=async()=>{r.value=!0;try{const e=await D();s.value=e.data||[]}catch(e){console.log(e),403===e.status?o.error("您没有权限执行此操作"):(console.error("获取角色列表失败",e),o.error("获取角色列表失败"))}finally{r.value=!1}};return l((()=>{f()})),{loading:r,roleList:s,roleFormRef:t,roleDialogVisible:i,isEdit:d,roleForm:n,roleRules:{roleName:[{required:!0,message:"请输入角色名称",trigger:"blur"}],roleCode:[{required:!0,message:"请输入角色编码",trigger:"blur"}]},permissionTree:u,permissionTreeData:c,permissionDialogVisible:m,handleAddRole:()=>{d.value=!1,n.id="",n.roleName="",n.roleCode="",n.description="",n.status=1,i.value=!0},handleEditRole:e=>{d.value=!0,n.id=e.id,n.roleName=e.roleName,n.roleCode=e.roleCode,n.description=e.description,n.status=e.status,i.value=!0},submitRoleForm:async()=>{t.value&&await t.value.validate((async e=>{if(e)try{d.value?(await N(n),o.success("更新成功")):(await x(n),o.success("添加成功")),i.value=!1,f()}catch(a){console.error(d.value?"更新角色失败":"添加角色失败",a),o.error(d.value?"更新角色失败":"添加角色失败")}}))},handleDeleteRole:async e=>{try{await U(e),o.success("删除成功"),f()}catch(a){console.error("删除角色失败",a),o.error("删除角色失败")}},handleSetPermission:async e=>{p.value=e.id,await(async()=>{try{const e=await T();c.value=e.data||[]}catch(e){console.error("获取权限树失败",e),o.error("获取权限树失败")}})(),m.value=!0,await(async e=>{try{const a=await P(e);u.value&&u.value.setCheckedKeys(a.data||[])}catch(a){console.error("获取角色权限失败",a),o.error("获取角色权限失败")}})(e.id)},saveRolePermissions:async()=>{if(u.value&&p.value)try{const e=u.value.getCheckedKeys(),a=u.value.getHalfCheckedKeys(),l=[...e,...a];await E(p.value,l),o.success("权限设置成功"),m.value=!1}catch(e){console.error("权限设置失败",e),o.error("权限设置失败")}}}}},K={class:"role-manage"},q={class:"role-list"},A={class:"header"},L=(e=>(v("data-v-5e03aae4"),e=e(),b(),e))((()=>d("span",null,"角色列表",-1))),S={class:"dialog-footer"},H={class:"dialog-footer"};z.render=function(e,a,l,o,v,b){const D=y,N=g,x=h,U=r,E=C,P=V,T=_,z=w,I=k,M=F,Z=R,B=s,G=j;return t(),i("div",K,[d("div",q,[n(P,null,{header:u((()=>[d("div",A,[L,n(D,{type:"primary",onClick:o.handleAddRole},{default:u((()=>[c("新增角色")])),_:1},8,["onClick"])])])),default:u((()=>[m((t(),p(E,{data:o.roleList,style:{width:"100%"}},{default:u((()=>[n(N,{prop:"roleName",label:"角色名称"}),n(N,{prop:"roleCode",label:"角色编码"}),n(N,{prop:"description",label:"角色描述"}),n(N,{label:"状态"},{default:u((e=>[n(x,{type:1===e.row.status?"success":"danger"},{default:u((()=>[c(f(1===e.row.status?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),n(N,{label:"操作",width:"280"},{default:u((e=>[n(D,{onClick:a=>o.handleEditRole(e.row),type:"primary",size:"small"},{default:u((()=>[c("编辑")])),_:2},1032,["onClick"]),n(D,{onClick:a=>o.handleSetPermission(e.row),type:"warning",size:"small"},{default:u((()=>[c("权限设置")])),_:2},1032,["onClick"]),n(U,{title:"确认删除?",onConfirm:a=>o.handleDeleteRole(e.row.id)},{reference:u((()=>[n(D,{type:"danger",size:"small"},{default:u((()=>[c("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"])),[[G,o.loading]])])),_:1})]),n(Z,{modelValue:o.roleDialogVisible,"onUpdate:modelValue":a[5]||(a[5]=e=>o.roleDialogVisible=e),title:o.isEdit?"编辑角色":"新增角色"},{footer:u((()=>[d("span",S,[n(D,{onClick:a[4]||(a[4]=e=>o.roleDialogVisible=!1)},{default:u((()=>[c("取消")])),_:1}),n(D,{type:"primary",onClick:o.submitRoleForm},{default:u((()=>[c("确定")])),_:1},8,["onClick"])])])),default:u((()=>[n(M,{model:o.roleForm,"label-width":"80px",rules:o.roleRules,ref:"roleFormRef"},{default:u((()=>[n(z,{label:"角色名称",prop:"roleName"},{default:u((()=>[n(T,{modelValue:o.roleForm.roleName,"onUpdate:modelValue":a[0]||(a[0]=e=>o.roleForm.roleName=e),placeholder:"请输入角色名称"},null,8,["modelValue"])])),_:1}),n(z,{label:"角色编码",prop:"roleCode"},{default:u((()=>[n(T,{modelValue:o.roleForm.roleCode,"onUpdate:modelValue":a[1]||(a[1]=e=>o.roleForm.roleCode=e),placeholder:"请输入角色编码"},null,8,["modelValue"])])),_:1}),n(z,{label:"角色描述",prop:"description"},{default:u((()=>[n(T,{modelValue:o.roleForm.description,"onUpdate:modelValue":a[2]||(a[2]=e=>o.roleForm.description=e),placeholder:"请输入角色描述",type:"textarea"},null,8,["modelValue"])])),_:1}),n(z,{label:"状态"},{default:u((()=>[n(I,{modelValue:o.roleForm.status,"onUpdate:modelValue":a[3]||(a[3]=e=>o.roleForm.status=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),n(Z,{modelValue:o.permissionDialogVisible,"onUpdate:modelValue":a[7]||(a[7]=e=>o.permissionDialogVisible=e),title:"权限设置"},{footer:u((()=>[d("span",H,[n(D,{onClick:a[6]||(a[6]=e=>o.permissionDialogVisible=!1)},{default:u((()=>[c("取消")])),_:1}),n(D,{type:"primary",onClick:o.saveRolePermissions},{default:u((()=>[c("确定")])),_:1},8,["onClick"])])])),default:u((()=>[n(B,{ref:"permissionTree",data:o.permissionTreeData,"show-checkbox":"","node-key":"id",props:{label:"name"}},null,8,["data"])])),_:1},8,["modelValue"])])},z.__scopeId="data-v-5e03aae4";export{z as default};
