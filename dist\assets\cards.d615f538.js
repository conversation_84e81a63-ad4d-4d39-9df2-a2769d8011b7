var e=Object.defineProperty,r=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,d=(r,a,t)=>a in r?e(r,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[a]=t;import{i}from"./index.431f094f.js";const c={pageQueryCard:e=>{const c=(p=((e,r)=>{for(var a in r||(r={}))o.call(r,a)&&d(e,a,r[a]);if(t)for(var a of t(r))s.call(r,a)&&d(e,a,r[a]);return e})({},e),n={pageNum:e.page,pageSize:e.size},r(p,a(n)));var p,n;return delete c.page,delete c.size,console.log("卡密查询参数:",c),i.get("/cards/pageQueryCard",{params:c,paramsSerializer:e=>Object.entries(e).filter((([e,r])=>void 0!==r)).map((([e,r])=>`${e}=${encodeURIComponent(r)}`)).join("&")})},deleteCard:e=>i.post("/cards/deleteCard",{id:e}),disableCard:e=>i.post("/cards/disableCard",{id:e}),enableCard:e=>i.post("/cards/enableCard",{id:e}),createCardSecret:e=>i.post("/cards/createCardSecret",e),batchCreateCards:e=>i.post("/cards/batchCreate",e),updateCardStatus:e=>i.put("/cards/updateStatus",e),getActiveCardsPage:e=>{const r={pageNum:e.current,pageSize:e.size};return delete r.current,delete r.size,console.log("活跃卡密查询参数:",r),i.get("/verifyPrice/getActiveCardsPage",{params:r,paramsSerializer:e=>Object.entries(e).filter((([e,r])=>void 0!==r)).map((([e,r])=>`${e}=${encodeURIComponent(r)}`)).join("&")})}};export{c};
