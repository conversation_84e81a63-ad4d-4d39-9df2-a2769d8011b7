var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,o=(a,l,r)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r;import{Z as s,a8 as t,_ as d,a3 as u,a9 as n,ac as i,af as p,ag as c,aa as m,ah as v,ai as f,ab as g,am as w,as as y,o as b,k as h,l as V,m as _,w as k,n as j,au as C,av as I,aw as P,v as U,at as x,F as z,an as O,H as E,G as L,ao as S,E as $,z as F}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                   *//* empty css                     *//* empty css                    *//* empty css                  *//* empty css                      *//* empty css                 *//* empty css                        */import{i as T}from"./index.34be5c94.js";import{g as q}from"./role.eae2613d.js";const B=()=>T.get("/user/list"),D=e=>T.get(`/user/get/${e}`),N=e=>T.post("/user/register",e),G=e=>T.put("/user/update",e),H=e=>T.delete(`/user/delete/${e}`);const K={class:"user-list-container"},R={class:"header-actions"},Z={class:"pagination-container"},A={class:"dialog-footer"},J={__name:"List",setup(e){const T=s([]),J=s(!1),M=s(""),Q=s(1),W=s(10),X=s(0),Y=s(!1),ee=s(!1),ae=s(!1),le=s([]),re=t({id:null,username:"",password:"",confirmPassword:"",nickname:"",phone:"",email:"",roleIds:[]}),oe=t({username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{validator:(e,a,l)=>{var r;ee.value||a?(""!==re.confirmPassword&&(null==(r=se.value)||r.validateField("confirmPassword")),l()):l(new Error("请输入密码"))},trigger:"blur"}],confirmPassword:[{validator:(e,a,l)=>{ee.value||a?a!==re.password?l(new Error("两次输入密码不一致")):l():l(new Error("请再次输入密码"))},trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],roleIds:[{required:!0,message:"请选择角色",trigger:"change",type:"array"}]}),se=s(null),te=async()=>{var e;try{J.value=!0;const a=await B();200===a.code?(T.value=a.data||[],X.value=(null==(e=a.data)?void 0:e.length)||0,M.value&&(T.value=T.value.filter((e=>{var a,l;return(null==(a=e.username)?void 0:a.toLowerCase().includes(M.value.toLowerCase()))||(null==(l=e.nickname)?void 0:l.toLowerCase().includes(M.value.toLowerCase()))})))):S.error(a.message||"获取用户列表失败")}catch(a){console.error("获取用户列表出错:",a),S.error(a.message||"获取用户列表失败")}finally{J.value=!1}},de=()=>{Q.value=1,te()},ue=e=>{pe(),e?(ee.value=!0,ne(e.id)):ee.value=!1,Y.value=!0},ne=async e=>{try{ae.value=!0;const a=await D(e);200===a.code&&a.data?(Object.keys(re).forEach((e=>{"password"!==e&&"confirmPassword"!==e&&void 0!==a.data[e]&&(re[e]=a.data[e])})),a.data.roleIds?re.roleIds=a.data.roleIds:a.data.roleId&&(re.roleIds=a.data.roleId?[a.data.roleId]:[])):(S.error(a.message||"获取用户信息失败"),Y.value=!1)}catch(a){console.error("获取用户信息出错:",a),S.error(a.message||"获取用户信息失败"),Y.value=!1}finally{ae.value=!1}},ie=async()=>{se.value&&await se.value.validate((async e=>{if(e)try{ae.value=!0;const e=((e,s)=>{for(var t in s||(s={}))l.call(s,t)&&o(e,t,s[t]);if(a)for(var t of a(s))r.call(s,t)&&o(e,t,s[t]);return e})({},re);let s;delete e.confirmPassword,ee.value&&!e.password&&delete e.password,ee.value?(console.log(e),s=await G(e)):s=await N(e),200===s.code?(S.success((ee.value?"更新":"添加")+"成功"),Y.value=!1,te()):S.error(s.message||(ee.value?"更新":"添加")+"失败")}catch(s){console.error((ee.value?"更新":"添加")+"用户出错:",s),S.error(s.message||(ee.value?"更新":"添加")+"失败")}finally{ae.value=!1}}))},pe=()=>{se.value&&se.value.resetFields(),Object.keys(re).forEach((e=>{re[e]="id"===e?null:""}))},ce=e=>{W.value=e,te()},me=e=>{Q.value=e,te()},ve=(e,a)=>{const l=e[a.property];if(!l)return"-";try{return new Date(l).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(r){return l}};return d((()=>{te(),(async()=>{try{const e=await q();console.log(e),200===e.code?le.value=e.data||[]:S.error(e.message||"获取角色列表失败")}catch(e){console.error("获取角色列表出错:",e),S.error(e.message||"获取角色列表失败")}})()})),(e,a)=>{const l=F,r=u,o=n,s=i,t=p,d=c,q=m,B=v,D=f,N=g,G=w,ne=y;return b(),h("div",K,[V("div",R,[_(o,{modelValue:M.value,"onUpdate:modelValue":a[0]||(a[0]=e=>M.value=e),placeholder:"请输入用户名搜索",clearable:"",class:"search-input",onClear:te,onKeyup:I(de,["enter"])},{append:k((()=>[_(r,{onClick:de},{default:k((()=>[_(l,null,{default:k((()=>[_(j(C))])),_:1})])),_:1})])),_:1},8,["modelValue"]),_(r,{type:"primary",onClick:a[1]||(a[1]=e=>ue())},{default:k((()=>[_(l,null,{default:k((()=>[_(j(P))])),_:1}),U("新增用户 ")])),_:1})]),x((b(),z(t,{data:T.value,border:"",style:{width:"100%"},"row-key":"id"},{default:k((()=>[_(s,{prop:"id",label:"ID",width:"80"}),_(s,{prop:"username",label:"用户名"}),_(s,{prop:"nickname",label:"昵称"}),_(s,{prop:"phone",label:"手机号"}),_(s,{prop:"email",label:"邮箱"}),_(s,{prop:"createTime",label:"创建时间",formatter:ve}),_(s,{label:"操作",width:"200",fixed:"right"},{default:k((({row:e})=>[_(r,{type:"primary",link:"",onClick:a=>ue(e)},{default:k((()=>[U(" 编辑 ")])),_:2},1032,["onClick"]),_(r,{type:"danger",link:"",onClick:a=>(e=>{$.confirm(`确定要删除用户 "${e.username||e.nickname||e.id}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const a=await H(e.id);200===a.code?(S.success("删除成功"),te()):S.error(a.message||"删除失败")}catch(a){console.error("删除用户出错:",a),S.error(a.message||"删除失败")}})).catch((()=>{}))})(e)},{default:k((()=>[U(" 删除 ")])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[ne,J.value]]),V("div",Z,[_(d,{"current-page":Q.value,"onUpdate:currentPage":a[2]||(a[2]=e=>Q.value=e),"page-size":W.value,"onUpdate:pageSize":a[3]||(a[3]=e=>W.value=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:X.value,onSizeChange:ce,onCurrentChange:me},null,8,["current-page","page-size","total"])]),_(G,{modelValue:Y.value,"onUpdate:modelValue":a[12]||(a[12]=e=>Y.value=e),title:ee.value?"编辑用户":"新增用户",width:"500px",onClosed:pe},{footer:k((()=>[V("span",A,[_(r,{onClick:a[11]||(a[11]=e=>Y.value=!1)},{default:k((()=>[U("取消")])),_:1}),_(r,{type:"primary",loading:ae.value,onClick:ie},{default:k((()=>[U("确定")])),_:1},8,["loading"])])])),default:k((()=>[_(N,{ref_key:"formRef",ref:se,model:re,rules:oe,"label-width":"100px",class:"user-form"},{default:k((()=>[_(q,{label:"用户名",prop:"username"},{default:k((()=>[_(o,{modelValue:re.username,"onUpdate:modelValue":a[4]||(a[4]=e=>re.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),ee.value?O("",!0):(b(),z(q,{key:0,label:"密码",prop:"password"},{default:k((()=>[_(o,{modelValue:re.password,"onUpdate:modelValue":a[5]||(a[5]=e=>re.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])])),_:1})),ee.value?O("",!0):(b(),z(q,{key:1,label:"确认密码",prop:"confirmPassword"},{default:k((()=>[_(o,{modelValue:re.confirmPassword,"onUpdate:modelValue":a[6]||(a[6]=e=>re.confirmPassword=e),type:"password",placeholder:"请确认密码","show-password":""},null,8,["modelValue"])])),_:1})),_(q,{label:"昵称",prop:"nickname"},{default:k((()=>[_(o,{modelValue:re.nickname,"onUpdate:modelValue":a[7]||(a[7]=e=>re.nickname=e),placeholder:"请输入昵称"},null,8,["modelValue"])])),_:1}),_(q,{label:"手机号",prop:"phone"},{default:k((()=>[_(o,{modelValue:re.phone,"onUpdate:modelValue":a[8]||(a[8]=e=>re.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])])),_:1}),_(q,{label:"邮箱",prop:"email"},{default:k((()=>[_(o,{modelValue:re.email,"onUpdate:modelValue":a[9]||(a[9]=e=>re.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1}),_(q,{label:"角色",prop:"roleIds"},{default:k((()=>[_(D,{modelValue:re.roleIds,"onUpdate:modelValue":a[10]||(a[10]=e=>re.roleIds=e),placeholder:"请选择角色",multiple:"","collapse-tags":""},{default:k((()=>[(b(!0),h(E,null,L(le.value,(e=>(b(),z(B,{key:e.id,label:e.roleName,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}},__scopeId:"data-v-1f0aa244"};export{J as default};
