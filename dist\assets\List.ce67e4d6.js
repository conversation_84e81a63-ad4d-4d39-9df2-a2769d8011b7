import{d as e,Z as a,a8 as n,$ as c,_ as t,a9 as i,aa as l,a3 as o,ab as s,ac as _,ad as r,ae as p,af as k,ag as d,ah as u,ai as m,aj as b,ak as f,al as x,am as y,o as v,k as h,l as g,m as V,w as j,v as w,t as z,an as U,F as C,ao as S,a6 as N,a7 as J}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css                      *//* empty css                        *//* empty css                     */import{i as O}from"./index.431f094f.js";import{A as F}from"./ActionBar.acef8039.js";import"./RefreshButton.c2d64dc7.js";const A=e=>O.post("/settledCostConfig/delete",null,{params:{id:e}}),T=e=>(console.log("API调用 - 新增入驻配置:",JSON.stringify(e,null,2)),O.post("/settledCostConfig/add",e,{headers:{"Content-Type":"application/json; charset=UTF-8"},validateStatus:function(e){return!0}}).then((e=>{if(console.log("API调用 - 新增响应状态:",e.status),console.log("API调用 - 新增响应数据:",e.data),e.status<200||e.status>=300){const a=new Error(`请求失败，状态码: ${e.status}`);throw a.response=e,a}return e})).catch((e=>{throw console.error("API调用 - 新增失败:"),console.error("- 错误消息:",e.message),e.response&&(console.error("- 响应状态:",e.response.status),console.error("- 响应数据:",e.response.data),console.error("- 响应头:",e.response.headers)),e}))),q=e=>(console.log("API调用 - 更新入驻配置:",JSON.stringify(e,null,2)),O.put("/settledCostConfig/update",e,{headers:{"Content-Type":"application/json; charset=UTF-8"},validateStatus:function(e){return!0}}).then((e=>{if(console.log("API调用 - 更新响应状态:",e.status),console.log("API调用 - 更新响应数据:",e.data),e.status<200||e.status>=300){const a=new Error(`请求失败，状态码: ${e.status}`);throw a.response=e,a}return e})).catch((e=>{throw console.error("API调用 - 更新失败:"),console.error("- 错误消息:",e.message),e.response&&(console.error("- 响应状态:",e.response.status),console.error("- 响应数据:",e.response.data),console.error("- 响应头:",e.response.headers)),e}))),I=e=>O.get("/settledCostConfig/search",{params:e}),P=e=>(N("data-v-5e7cf10f"),e=e(),J(),e),E={class:"list-container"},R={class:"search-area"},B={key:0},K={key:0,class:"expand-container"},$={class:"expand-block"},D=P((()=>g("h4",{class:"expand-title"},"基本约束",-1))),L={class:"expand-row-item"},Z=P((()=>g("span",{class:"expand-label"},"最小本数:",-1))),G={class:"expand-value"},H={class:"expand-row-item"},M=P((()=>g("span",{class:"expand-label"},"最大本数:",-1))),Q={class:"expand-value"},W={key:0,class:"expand-block"},X=P((()=>g("h4",{class:"expand-title"},"小程序收费",-1))),Y={class:"expand-row-item"},ee=P((()=>g("span",{class:"expand-label"},"月收费:",-1))),ae={class:"expand-value"},ne={class:"expand-row-item"},ce=P((()=>g("span",{class:"expand-label"},"年收费:",-1))),te={class:"expand-value"},ie={class:"expand-row-item"},le=P((()=>g("span",{class:"expand-label"},"成本:",-1))),oe={class:"expand-value"},se={class:"expand-row-item"},_e=P((()=>g("span",{class:"expand-label"},"推广员佣金类型:",-1))),re={class:"expand-value"},pe={key:0,class:"expand-row-item"},ke=P((()=>g("span",{class:"expand-label"},"推广员提点:",-1))),de={class:"expand-value"},ue={key:1,class:"expand-row-item"},me=P((()=>g("span",{class:"expand-label"},"月推广员佣金:",-1))),be={class:"expand-value"},fe={key:2,class:"expand-row-item"},xe=P((()=>g("span",{class:"expand-label"},"年推广员佣金:",-1))),ye={class:"expand-value"},ve={key:3,class:"expand-row-item"},he=P((()=>g("span",{class:"expand-label"},"推广员固费:",-1))),ge={class:"expand-value"},Ve={class:"expand-row-item"},je=P((()=>g("span",{class:"expand-label"},"会员佣金类型:",-1))),we={class:"expand-value"},ze={key:4,class:"expand-row-item"},Ue=P((()=>g("span",{class:"expand-label"},"会员提点:",-1))),Ce={class:"expand-value"},Se={key:5,class:"expand-row-item"},Ne=P((()=>g("span",{class:"expand-label"},"月会员佣金:",-1))),Je={class:"expand-value"},Oe={key:6,class:"expand-row-item"},Fe=P((()=>g("span",{class:"expand-label"},"年会员佣金:",-1))),Ae={class:"expand-value"},Te={key:7,class:"expand-row-item"},qe=P((()=>g("span",{class:"expand-label"},"会员固费:",-1))),Ie={class:"expand-value"},Pe={key:1,class:"expand-block"},Ee=P((()=>g("h4",{class:"expand-title"},"账号收费",-1))),Re={class:"expand-row-item"},Be=P((()=>g("span",{class:"expand-label"},"月收费:",-1))),Ke={class:"expand-value"},$e={class:"expand-row-item"},De=P((()=>g("span",{class:"expand-label"},"年收费:",-1))),Le={class:"expand-value"},Ze={class:"expand-row-item"},Ge=P((()=>g("span",{class:"expand-label"},"成本:",-1))),He={class:"expand-value"},Me={class:"expand-row-item"},Qe=P((()=>g("span",{class:"expand-label"},"推广员佣金类型:",-1))),We={class:"expand-value"},Xe={key:0,class:"expand-row-item"},Ye=P((()=>g("span",{class:"expand-label"},"推广员提点:",-1))),ea={class:"expand-value"},aa={key:1,class:"expand-row-item"},na=P((()=>g("span",{class:"expand-label"},"月推广员佣金:",-1))),ca={class:"expand-value"},ta={key:2,class:"expand-row-item"},ia=P((()=>g("span",{class:"expand-label"},"年推广员佣金:",-1))),la={class:"expand-value"},oa={key:3,class:"expand-row-item"},sa=P((()=>g("span",{class:"expand-label"},"推广员固费:",-1))),_a={class:"expand-value"},ra={class:"expand-row-item"},pa=P((()=>g("span",{class:"expand-label"},"会员佣金类型:",-1))),ka={class:"expand-value"},da={key:4,class:"expand-row-item"},ua=P((()=>g("span",{class:"expand-label"},"会员提点:",-1))),ma={class:"expand-value"},ba={key:5,class:"expand-row-item"},fa=P((()=>g("span",{class:"expand-label"},"月会员佣金:",-1))),xa={class:"expand-value"},ya={key:6,class:"expand-row-item"},va=P((()=>g("span",{class:"expand-label"},"年会员佣金:",-1))),ha={class:"expand-value"},ga={key:7,class:"expand-row-item"},Va=P((()=>g("span",{class:"expand-label"},"会员固费:",-1))),ja={class:"expand-value"},wa={key:2,class:"expand-block"},za=P((()=>g("h4",{class:"expand-title"},"孔夫子店铺",-1))),Ua={class:"expand-row-item"},Ca=P((()=>g("span",{class:"expand-label"},"月收费:",-1))),Sa={class:"expand-value"},Na={class:"expand-row-item"},Ja=P((()=>g("span",{class:"expand-label"},"年收费:",-1))),Oa={class:"expand-value"},Fa={class:"expand-row-item"},Aa=P((()=>g("span",{class:"expand-label"},"成本:",-1))),Ta={class:"expand-value"},qa={class:"expand-row-item"},Ia=P((()=>g("span",{class:"expand-label"},"佣金类型:",-1))),Pa={class:"expand-value"},Ea={key:0,class:"expand-row-item"},Ra=P((()=>g("span",{class:"expand-label"},"提点比例:",-1))),Ba={class:"expand-value"},Ka={key:1,class:"expand-row-item"},$a=P((()=>g("span",{class:"expand-label"},"月推广员佣金:",-1))),Da={class:"expand-value"},La={key:2,class:"expand-row-item"},Za=P((()=>g("span",{class:"expand-label"},"年推广员佣金:",-1))),Ga={class:"expand-value"},Ha={key:3,class:"expand-row-item"},Ma=P((()=>g("span",{class:"expand-label"},"固定费用:",-1))),Qa={class:"expand-value"},Wa={class:"expand-row-item"},Xa=P((()=>g("span",{class:"expand-label"},"会员佣金类型:",-1))),Ya={class:"expand-value"},en={key:4,class:"expand-row-item"},an=P((()=>g("span",{class:"expand-label"},"会员提点:",-1))),nn={class:"expand-value"},cn={key:5,class:"expand-row-item"},tn=P((()=>g("span",{class:"expand-label"},"月会员佣金:",-1))),ln={class:"expand-value"},on={key:6,class:"expand-row-item"},sn=P((()=>g("span",{class:"expand-label"},"年会员佣金:",-1))),_n={class:"expand-value"},rn={key:7,class:"expand-row-item"},pn=P((()=>g("span",{class:"expand-label"},"会员固费:",-1))),kn={class:"expand-value"},dn={key:3,class:"expand-block"},un=P((()=>g("h4",{class:"expand-title"},"拼多多专营店",-1))),mn={class:"expand-row-item"},bn=P((()=>g("span",{class:"expand-label"},"月收费:",-1))),fn={class:"expand-value"},xn={class:"expand-row-item"},yn=P((()=>g("span",{class:"expand-label"},"年收费:",-1))),vn={class:"expand-value"},hn={class:"expand-row-item"},gn=P((()=>g("span",{class:"expand-label"},"成本:",-1))),Vn={class:"expand-value"},jn={class:"expand-row-item"},wn=P((()=>g("span",{class:"expand-label"},"佣金类型:",-1))),zn={class:"expand-value"},Un={key:0,class:"expand-row-item"},Cn=P((()=>g("span",{class:"expand-label"},"提点比例:",-1))),Sn={class:"expand-value"},Nn={key:1,class:"expand-row-item"},Jn=P((()=>g("span",{class:"expand-label"},"月推广员佣金:",-1))),On={class:"expand-value"},Fn={key:2,class:"expand-row-item"},An=P((()=>g("span",{class:"expand-label"},"年推广员佣金:",-1))),Tn={class:"expand-value"},qn={key:3,class:"expand-row-item"},In=P((()=>g("span",{class:"expand-label"},"固定费用:",-1))),Pn={class:"expand-value"},En={class:"expand-row-item"},Rn=P((()=>g("span",{class:"expand-label"},"会员佣金类型:",-1))),Bn={class:"expand-value"},Kn={key:4,class:"expand-row-item"},$n=P((()=>g("span",{class:"expand-label"},"会员提点:",-1))),Dn={class:"expand-value"},Ln={key:5,class:"expand-row-item"},Zn=P((()=>g("span",{class:"expand-label"},"月会员佣金:",-1))),Gn={class:"expand-value"},Hn={key:6,class:"expand-row-item"},Mn=P((()=>g("span",{class:"expand-label"},"年会员佣金:",-1))),Qn={class:"expand-value"},Wn={key:7,class:"expand-row-item"},Xn=P((()=>g("span",{class:"expand-label"},"会员固费:",-1))),Yn={class:"expand-value"},ec={key:4,class:"expand-block"},ac=P((()=>g("h4",{class:"expand-title"},"资源费分级",-1))),nc={class:"expand-row-item"},cc=P((()=>g("span",{class:"expand-label"},"3000条以下:",-1))),tc={class:"expand-value"},ic={class:"expand-row-item"},lc=P((()=>g("span",{class:"expand-label"},"3000-20000:",-1))),oc={class:"expand-value"},sc={class:"expand-row-item"},_c=P((()=>g("span",{class:"expand-label"},"3000-20000成本:",-1))),rc={class:"expand-value"},pc={class:"expand-row-item"},kc=P((()=>g("span",{class:"expand-label"},"20000-50000:",-1))),dc={class:"expand-value"},uc={class:"expand-row-item"},mc=P((()=>g("span",{class:"expand-label"},"20000-50000成本:",-1))),bc={class:"expand-value"},fc={class:"expand-row-item"},xc=P((()=>g("span",{class:"expand-label"},"50000-100000:",-1))),yc={class:"expand-value"},vc={class:"expand-row-item"},hc=P((()=>g("span",{class:"expand-label"},"50000-100000成本:",-1))),gc={class:"expand-value"},Vc={class:"expand-row-item"},jc=P((()=>g("span",{class:"expand-label"},"100000条以上:",-1))),wc={class:"expand-value"},zc={class:"expand-row-item"},Uc=P((()=>g("span",{class:"expand-label"},"成本:",-1))),Cc={class:"expand-value"},Sc={class:"expand-row-item"},Nc=P((()=>g("span",{class:"expand-label"},"佣金类型:",-1))),Jc={class:"expand-value"},Oc={key:0,class:"expand-row-item"},Fc=P((()=>g("span",{class:"expand-label"},"提点比例:",-1))),Ac={class:"expand-value"},Tc={key:1,class:"expand-row-item"},qc=P((()=>g("span",{class:"expand-label"},"3000条以下佣金:",-1))),Ic={class:"expand-value"},Pc={key:2,class:"expand-row-item"},Ec=P((()=>g("span",{class:"expand-label"},"3000-20000佣金:",-1))),Rc={class:"expand-value"},Bc={key:3,class:"expand-row-item"},Kc=P((()=>g("span",{class:"expand-label"},"固定费用:",-1))),$c={class:"expand-value"},Dc={class:"expand-row-item"},Lc=P((()=>g("span",{class:"expand-label"},"会员佣金类型:",-1))),Zc={class:"expand-value"},Gc={key:4,class:"expand-row-item"},Hc=P((()=>g("span",{class:"expand-label"},"会员提点:",-1))),Mc={class:"expand-value"},Qc={key:5,class:"expand-row-item"},Wc=P((()=>g("span",{class:"expand-label"},"3000条以下会员佣金:",-1))),Xc={class:"expand-value"},Yc={key:6,class:"expand-row-item"},et=P((()=>g("span",{class:"expand-label"},"3000-20000会员佣金:",-1))),at={class:"expand-value"},nt={key:7,class:"expand-row-item"},ct=P((()=>g("span",{class:"expand-label"},"会员固费:",-1))),tt={class:"expand-value"},it={key:5,class:"expand-block"},lt=P((()=>g("h4",{class:"expand-title"},"交易手续费",-1))),ot={class:"expand-row-item"},st=P((()=>g("span",{class:"expand-label"},"同库房同店铺:",-1))),_t={class:"expand-value"},rt={class:"expand-row-item"},pt=P((()=>g("span",{class:"expand-label"},"成本:",-1))),kt={class:"expand-value"},dt={class:"expand-row-item"},ut=P((()=>g("span",{class:"expand-label"},"佣金类型:",-1))),mt={class:"expand-value"},bt={key:6,class:"expand-block"},ft=P((()=>g("h4",{class:"expand-title"},"同库房不同店铺",-1))),xt={class:"expand-row-item"},yt=P((()=>g("span",{class:"expand-label"},"扣库房:",-1))),vt={class:"expand-value"},ht={class:"expand-row-item"},gt=P((()=>g("span",{class:"expand-label"},"扣店主:",-1))),Vt={class:"expand-value"},jt={class:"expand-row-item"},wt=P((()=>g("span",{class:"expand-label"},"成本:",-1))),zt={class:"expand-value"},Ut={class:"expand-row-item"},Ct=P((()=>g("span",{class:"expand-label"},"佣金类型:",-1))),St={class:"expand-value"},Nt={key:1},Jt=[P((()=>g("span",{style:{color:"red"}},"约束条件格式错误",-1)))],Ot={key:1},Ft=[P((()=>g("span",{style:{color:"#999"}},"无约束条件",-1)))],At={key:0},Tt={key:0},qt={key:1},It={key:1},Pt={key:0},Et={key:0},Rt={key:1},Bt={key:1},Kt={key:0},$t={key:0},Dt={key:1},Lt={key:1},Zt={class:"pagination"},Gt=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Ht=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Mt=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Qt=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),Wt=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Xt=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),Yt=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),ei=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),ai=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),ni=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),ci=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),ti=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),ii=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),li=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),oi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),si=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),_i=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),ri=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),pi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),ki=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),di=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),ui=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),mi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),bi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),fi=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),xi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),yi=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),vi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),hi=P((()=>g("span",{style:{"margin-left":"8px"}},"元/月 (默认为0免费)",-1))),gi=P((()=>g("span",{style:{"margin-left":"8px"}},"元/月 (默认为50)",-1))),Vi=P((()=>g("span",{style:{"margin-left":"8px"}},"元/月 (默认为100)",-1))),ji=P((()=>g("span",{style:{"margin-left":"8px"}},"元/月 (默认为200)",-1))),wi=P((()=>g("span",{style:{"margin-left":"8px"}},"元/月 (默认为300)",-1))),zi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Ui=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),Ci=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Si=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),Ni=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Ji=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Oi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Fi=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),Ai=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Ti=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),qi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Ii=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),Pi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Ei=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),Ri=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Bi=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Ki=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),$i=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Di=P((()=>g("span",{style:{"margin-left":"8px"}},"%",-1))),Li=P((()=>g("span",{style:{"margin-left":"8px"}},"元",-1))),Zi={class:"dialog-footer"};var Gi=e({__name:"List",setup(e){const N=a([]),J=n({settled_cost_key:"",title:""}),O=n({current:1,size:10,total:0}),P=a([]),Gi=a(null),Hi=e=>["预留","提点","固定费用","提点/固费"][e]||"未知",Mi=e=>{try{return JSON.parse(e)}catch{return null}},Qi=(e,a)=>{try{if(!e)return 0;return JSON.parse(e)[a]||0}catch{return 0}},Wi=async()=>{try{const e={pageNum:O.current,pageSize:O.size,settledCostKey:J.settled_cost_key||void 0,title:J.title||void 0},a=await I(e);200===a.code&&a.data?(N.value=a.data.list.map((e=>({id:e.id,title:e.title,settled_cost_key:e.settledCostKey,constraint_json:e.constraintJson,kickback_type:e.kickbackType,kickback_value:e.kickbackValue,resource_cost_type:e.resourceCostType,resource_cost_value:e.resourceCostValue,service_rate:e.serviceRate,price:e.price/100,state:e.state,note:e.note,created_by:e.createdBy,created_time:e.createdTime,updated_by:e.updatedBy,updated_time:e.updatedTime,is_del:e.isDel}))),O.total=a.data.total||0):(S.error(a.message||"获取数据失败"),N.value=[],O.total=0)}catch(e){console.error("获取数据失败:",e),S.error("获取数据失败"),N.value=[],O.total=0}},Xi=()=>{O.current=1,Wi()},Yi=()=>{J.settled_cost_key="",J.title="",O.current=1,Wi()},el=e=>{O.size=e,O.current=1,Wi()},al=e=>{O.current=e,Wi()},nl=e=>{P.value=e},cl=()=>{Gi.value&&(P.value.length===N.value.length&&N.value.length>0?N.value.forEach((e=>{Gi.value.toggleRowSelection(e,!1)})):N.value.forEach((e=>{Gi.value.toggleRowSelection(e,!0)})))},tl=a(!1),il=a("add"),ll=a(),ol=n({id:0,title:"",settled_cost_key:"",constraint_json:"",books_count_min:0,books_count_max:0,member_type:1,kickback_type:0,kickback_value:"",kickback_point:0,kickback_fixed:0,resource_cost_type:0,resource_cost_value:0,service_rate:0,price:0,state:1,note:"",miniapp_month_fee:0,miniapp_cost:0,miniapp_year_fee:0,miniapp_kickback_type:0,miniapp_kickback_point:0,miniapp_kickback_fixed:0,miniapp_member_kickback_type:0,miniapp_member_kickback_point:0,miniapp_member_kickback_fixed:0,account_month_fee:0,account_cost:0,account_year_fee:0,account_kickback_type:0,account_kickback_point:0,account_kickback_fixed:0,account_member_kickback_type:0,account_member_kickback_point:0,account_member_kickback_fixed:0,kongfz_month_fee:0,kongfz_cost:0,kongfz_year_fee:0,kongfz_kickback_type:0,kongfz_kickback_point:0,kongfz_kickback_fixed:0,kongfz_member_kickback_type:0,kongfz_member_kickback_point:0,kongfz_member_kickback_fixed:0,pdd_month_fee:0,pdd_cost:0,pdd_year_fee:0,pdd_kickback_type:0,pdd_kickback_point:0,pdd_kickback_fixed:0,pdd_member_kickback_type:0,pdd_member_kickback_point:0,pdd_member_kickback_fixed:0,resource_tier1_fee:0,resource_tier2_fee:50,resource_tier3_fee:100,resource_tier4_fee:200,resource_tier5_fee:300,resource_tier_cost:0,resource_tier_kickback_type:0,resource_tier_member_kickback_type:0,transaction_same_warehouse_shop:0,transaction_cost:0,transaction_same_kickback_type:0,transaction_same_kickback_point:0,transaction_same_kickback_fixed:0,transaction_same_member_kickback_type:0,transaction_same_member_kickback_point:0,transaction_same_member_kickback_fixed:0,different_shop_warehouse_percent:0,different_shop_warehouse_fixed:.02,different_shop_owner_percent:0,different_shop_owner_fixed:.02,different_shop_cost:0,different_shop_kickback_type:0,different_shop_member_kickback_type:0,resource_tier_kickback_point:0,resource_tier_kickback_fixed:0,resource_tier_member_kickback_point:0,resource_tier_member_kickback_fixed:0,different_shop_kickback_point:0,different_shop_kickback_fixed:0,different_shop_member_kickback_point:0,different_shop_member_kickback_fixed:0}),sl={title:[{required:!0,message:"请输入标题",trigger:"blur"}],settled_cost_key:[{required:!0,message:"请输入入驻标识",trigger:"blur"}],books_count_min:[{required:!0,message:"请输入最小本数",trigger:"blur"},{validator:(e,a,n)=>{a>=ol.books_count_max?n(new Error("最小本数必须小于最大本数")):n()},trigger:"blur"}],books_count_max:[{required:!0,message:"请输入最大本数",trigger:"blur"},{validator:(e,a,n)=>{a<=ol.books_count_min?n(new Error("最大本数必须大于最小本数")):n()},trigger:"blur"}],kickback_type:[{required:!0,message:"请选择佣金类型",trigger:"change"}],kickback_point:[{validator:(e,a,n)=>{[1,3].includes(ol.kickback_type)&&null==a?n(new Error("请输入提点比例")):n()},trigger:"blur"}],kickback_fixed:[{validator:(e,a,n)=>{[2,3].includes(ol.kickback_type)&&null==a?n(new Error("请输入固定费用")):n()},trigger:"blur"}],resource_cost_type:[{required:!0,message:"请选择资源费类型",trigger:"change"}],resource_cost_value:[{required:!0,message:"请输入资源费值",trigger:"blur"}],service_rate:[{required:!0,message:"请输入服务费率",trigger:"blur"}],price:[{required:!0,message:"请输入价格",trigger:"blur"}]},_l=()=>{ol.kickback_value=JSON.stringify({point:ol.kickback_point,fixed:ol.kickback_fixed})},rl=()=>{},pl=()=>{},kl=()=>{},dl=()=>{},ul=()=>{},ml=()=>{},bl=()=>{},fl=()=>{},xl=()=>{},yl=()=>{},vl=()=>{},hl=()=>{},gl=()=>{},Vl=()=>{};c((()=>ol.kickback_type),(()=>{_l()}));const jl=()=>{Object.assign(ol,{id:0,title:"",settled_cost_key:"",constraint_json:"",books_count_min:0,books_count_max:0,member_type:1,kickback_type:0,kickback_value:"",kickback_point:0,kickback_fixed:0,resource_cost_type:0,resource_cost_value:0,service_rate:0,price:0,state:1,note:"",miniapp_month_fee:0,miniapp_cost:0,miniapp_year_fee:0,miniapp_kickback_type:0,miniapp_kickback_point:0,miniapp_kickback_fixed:0,miniapp_member_kickback_type:0,miniapp_member_kickback_point:0,miniapp_member_kickback_fixed:0,account_month_fee:0,account_cost:0,account_year_fee:0,account_kickback_type:0,account_kickback_point:0,account_kickback_fixed:0,account_member_kickback_type:0,account_member_kickback_point:0,account_member_kickback_fixed:0,kongfz_month_fee:0,kongfz_cost:0,kongfz_year_fee:0,kongfz_kickback_type:0,kongfz_kickback_point:0,kongfz_kickback_fixed:0,kongfz_member_kickback_type:0,kongfz_member_kickback_point:0,kongfz_member_kickback_fixed:0,pdd_month_fee:0,pdd_cost:0,pdd_year_fee:0,pdd_kickback_type:0,pdd_kickback_point:0,pdd_kickback_fixed:0,pdd_member_kickback_type:0,pdd_member_kickback_point:0,pdd_member_kickback_fixed:0,resource_tier1_fee:0,resource_tier2_fee:50,resource_tier3_fee:100,resource_tier4_fee:200,resource_tier5_fee:300,resource_tier_cost:0,resource_tier_kickback_type:0,resource_tier_member_kickback_type:0,transaction_same_warehouse_shop:0,transaction_cost:0,transaction_same_kickback_type:0,transaction_same_kickback_point:0,transaction_same_kickback_fixed:0,transaction_same_member_kickback_type:0,transaction_same_member_kickback_point:0,transaction_same_member_kickback_fixed:0,different_shop_warehouse_percent:0,different_shop_warehouse_fixed:.02,different_shop_owner_percent:0,different_shop_owner_fixed:.02,different_shop_cost:0,different_shop_kickback_type:0,different_shop_member_kickback_type:0,resource_tier_kickback_point:0,resource_tier_kickback_fixed:0,resource_tier_member_kickback_point:0,resource_tier_member_kickback_fixed:0,different_shop_kickback_point:0,different_shop_kickback_fixed:0,different_shop_member_kickback_point:0,different_shop_member_kickback_fixed:0})},wl=()=>{il.value="add",jl(),tl.value=!0},zl=a(!1),Ul=async()=>{ll.value&&(zl.value=!0,await ll.value.validate((async e=>{if(e)try{_l();const e={books_count_min:ol.books_count_min,books_count_max:ol.books_count_max,member_type:ol.member_type,miniapp:{month_fee:ol.miniapp_month_fee,cost:ol.miniapp_cost,year_fee:ol.miniapp_year_fee,kickback_type:ol.miniapp_kickback_type,kickback_value:JSON.stringify({point:ol.miniapp_kickback_point,fixed:ol.miniapp_kickback_fixed}),member_kickback_type:ol.miniapp_member_kickback_type,member_kickback_value:JSON.stringify({point:ol.miniapp_member_kickback_point,fixed:ol.miniapp_member_kickback_fixed})},account:{month_fee:ol.account_month_fee,cost:ol.account_cost,year_fee:ol.account_year_fee,kickback_type:ol.account_kickback_type,kickback_value:JSON.stringify({point:ol.account_kickback_point,fixed:ol.account_kickback_fixed}),member_kickback_type:ol.account_member_kickback_type,member_kickback_value:JSON.stringify({point:ol.account_member_kickback_point,fixed:ol.account_member_kickback_fixed})},kongfz:{month_fee:ol.kongfz_month_fee,cost:ol.kongfz_cost,year_fee:ol.kongfz_year_fee,kickback_type:ol.kongfz_kickback_type,kickback_value:JSON.stringify({point:ol.kongfz_kickback_point,fixed:ol.kongfz_kickback_fixed}),member_kickback_type:ol.kongfz_member_kickback_type,member_kickback_value:JSON.stringify({point:ol.kongfz_member_kickback_point,fixed:ol.kongfz_member_kickback_fixed})},pdd:{month_fee:ol.pdd_month_fee,cost:ol.pdd_cost,year_fee:ol.pdd_year_fee,kickback_type:ol.pdd_kickback_type,kickback_value:JSON.stringify({point:ol.pdd_kickback_point,fixed:ol.pdd_kickback_fixed}),member_kickback_type:ol.pdd_member_kickback_type,member_kickback_value:JSON.stringify({point:ol.pdd_member_kickback_point,fixed:ol.pdd_member_kickback_fixed})},resource_tier:{tier1_fee:ol.resource_tier1_fee,tier2_fee:ol.resource_tier2_fee,tier3_fee:ol.resource_tier3_fee,tier4_fee:ol.resource_tier4_fee,tier5_fee:ol.resource_tier5_fee,cost:ol.resource_tier_cost,kickback_type:ol.resource_tier_kickback_type,kickback_value:JSON.stringify({point:ol.resource_tier_kickback_point,fixed:ol.resource_tier_kickback_fixed}),member_kickback_type:ol.resource_tier_member_kickback_type,member_kickback_value:JSON.stringify({point:ol.resource_tier_member_kickback_point,fixed:ol.resource_tier_member_kickback_fixed})},transaction:{same_warehouse_shop:ol.transaction_same_warehouse_shop,cost:ol.transaction_cost,kickback_type:ol.transaction_same_kickback_type,kickback_value:JSON.stringify({point:ol.transaction_same_kickback_point,fixed:ol.transaction_same_kickback_fixed}),member_kickback_type:ol.transaction_same_member_kickback_type,member_kickback_value:JSON.stringify({point:ol.transaction_same_member_kickback_point,fixed:ol.transaction_same_member_kickback_fixed})},different_shop:{warehouse_percent:ol.different_shop_warehouse_percent,warehouse_fixed:ol.different_shop_warehouse_fixed,owner_percent:ol.different_shop_owner_percent,owner_fixed:ol.different_shop_owner_fixed,cost:ol.different_shop_cost,kickback_type:ol.different_shop_kickback_type,kickback_value:JSON.stringify({point:ol.different_shop_kickback_point,fixed:ol.different_shop_kickback_fixed}),member_kickback_type:ol.different_shop_member_kickback_type,member_kickback_value:JSON.stringify({point:ol.different_shop_member_kickback_point,fixed:ol.different_shop_member_kickback_fixed})}},a={title:ol.title,settledCostKey:ol.settled_cost_key,constraintJson:JSON.stringify(e),kickbackType:ol.kickback_type,kickbackValue:ol.kickback_value,resourceCostType:ol.resource_cost_type,resourceCostValue:ol.resource_cost_value,serviceRate:ol.service_rate,price:ol.price,state:ol.state,note:ol.note||""};if("edit"===il.value&&(a.id=ol.id),console.log("提交数据:",JSON.stringify(a,null,2)),"add"===il.value){const e=await T(a);console.log("新增接口响应:",e),S.success("新增成功")}else{const e=await q(a);console.log("更新接口响应:",e),S.success("修改成功")}tl.value=!1,await Wi()}catch(a){console.error("提交失败:",a),a.response&&(console.error("错误状态码:",a.response.status),console.error("错误响应头:",a.response.headers),console.error("错误响应数据:",a.response.data));let e="提交失败";if(a.response&&a.response.data){const n=a.response.data;console.log("完整错误响应:",n),n.message?e=n.message:n.error&&(e=n.error)}else a.message&&(e=a.message);S.error(e)}finally{zl.value=!1}else zl.value=!1})))},Cl=()=>{Wi()};return t((()=>{Wi()})),(e,a)=>{const n=i,c=l,t=o,T=s,q=_,I=r,P=p,_l=k,Sl=d,Nl=u,Jl=m,Ol=b,Fl=f,Al=x,Tl=y;return v(),h("div",E,[g("div",R,[V(T,{inline:!0,model:J},{default:j((()=>[V(c,{label:"入驻标识"},{default:j((()=>[V(n,{modelValue:J.settled_cost_key,"onUpdate:modelValue":a[0]||(a[0]=e=>J.settled_cost_key=e),placeholder:"请输入入驻标识",clearable:""},null,8,["modelValue"])])),_:1}),V(c,{label:"标题"},{default:j((()=>[V(n,{modelValue:J.title,"onUpdate:modelValue":a[1]||(a[1]=e=>J.title=e),placeholder:"请输入标题",clearable:""},null,8,["modelValue"])])),_:1}),V(c,null,{default:j((()=>[V(t,{type:"primary",onClick:Xi},{default:j((()=>[w("搜索")])),_:1}),V(t,{onClick:Yi},{default:j((()=>[w("重置")])),_:1})])),_:1})])),_:1},8,["model"])]),V(F,{onRefresh:Cl},{left:j((()=>[V(t,{type:"primary",onClick:wl},{default:j((()=>[w("新增")])),_:1}),V(t,{onClick:cl},{default:j((()=>[w("全选/反选")])),_:1})])),_:1}),V(_l,{ref_key:"tableRef",ref:Gi,data:N.value,border:"",stripe:"",style:{width:"100%"},onSelectionChange:nl,"row-key":"id"},{default:j((()=>[V(q,{type:"expand"},{default:j((({row:e})=>[e.constraint_json?(v(),h("div",B,[Mi(e.constraint_json)?(v(),h("div",K,[g("div",$,[D,g("div",L,[Z,g("span",G,z(Mi(e.constraint_json).books_count_min),1)]),g("div",H,[M,g("span",Q,z(Mi(e.constraint_json).books_count_max),1)])]),Mi(e.constraint_json).miniapp?(v(),h("div",W,[X,g("div",Y,[ee,g("span",ae,z(Mi(e.constraint_json).miniapp.month_fee||0)+"元 ",1)]),g("div",ne,[ce,g("span",te,z(Mi(e.constraint_json).miniapp.year_fee||0)+"元 ",1)]),g("div",ie,[le,g("span",oe,z(Mi(e.constraint_json).miniapp.cost||0)+"元 ",1)]),g("div",se,[_e,g("span",re,z(Hi(Mi(e.constraint_json).miniapp.kickback_type||0)),1)]),[1,3].includes(Mi(e.constraint_json).miniapp.kickback_type||0)?(v(),h("div",pe,[ke,g("span",de,z(Qi(Mi(e.constraint_json).miniapp.kickback_value||"{}","point"))+"% ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).miniapp.kickback_type||0)?(v(),h("div",ue,[me,g("span",be,z((Mi(e.constraint_json).miniapp.month_fee*Qi(Mi(e.constraint_json).miniapp.kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).miniapp.kickback_type||0)?(v(),h("div",fe,[xe,g("span",ye,z((Mi(e.constraint_json).miniapp.year_fee*Qi(Mi(e.constraint_json).miniapp.kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[2,3].includes(Mi(e.constraint_json).miniapp.kickback_type||0)?(v(),h("div",ve,[he,g("span",ge,z(Qi(Mi(e.constraint_json).miniapp.kickback_value||"{}","fixed"))+"元 ",1)])):U("",!0),g("div",Ve,[je,g("span",we,z(Hi(Mi(e.constraint_json).miniapp.member_kickback_type||0)),1)]),[1,3].includes(Mi(e.constraint_json).miniapp.member_kickback_type||0)?(v(),h("div",ze,[Ue,g("span",Ce,z(Qi(Mi(e.constraint_json).miniapp.member_kickback_value||"{}","point"))+"% ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).miniapp.member_kickback_type||0)?(v(),h("div",Se,[Ne,g("span",Je,z((Mi(e.constraint_json).miniapp.month_fee*Qi(Mi(e.constraint_json).miniapp.member_kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).miniapp.member_kickback_type||0)?(v(),h("div",Oe,[Fe,g("span",Ae,z((Mi(e.constraint_json).miniapp.year_fee*Qi(Mi(e.constraint_json).miniapp.member_kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[2,3].includes(Mi(e.constraint_json).miniapp.member_kickback_type||0)?(v(),h("div",Te,[qe,g("span",Ie,z(Qi(Mi(e.constraint_json).miniapp.member_kickback_value||"{}","fixed"))+"元 ",1)])):U("",!0)])):U("",!0),Mi(e.constraint_json).account?(v(),h("div",Pe,[Ee,g("div",Re,[Be,g("span",Ke,z(Mi(e.constraint_json).account.month_fee||0)+"元 ",1)]),g("div",$e,[De,g("span",Le,z(Mi(e.constraint_json).account.year_fee||0)+"元 ",1)]),g("div",Ze,[Ge,g("span",He,z(Mi(e.constraint_json).account.cost||0)+"元 ",1)]),g("div",Me,[Qe,g("span",We,z(Hi(Mi(e.constraint_json).account.kickback_type||0)),1)]),[1,3].includes(Mi(e.constraint_json).account.kickback_type||0)?(v(),h("div",Xe,[Ye,g("span",ea,z(Qi(Mi(e.constraint_json).account.kickback_value||"{}","point"))+"% ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).account.kickback_type||0)?(v(),h("div",aa,[na,g("span",ca,z((Mi(e.constraint_json).account.month_fee*Qi(Mi(e.constraint_json).account.kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).account.kickback_type||0)?(v(),h("div",ta,[ia,g("span",la,z((Mi(e.constraint_json).account.year_fee*Qi(Mi(e.constraint_json).account.kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[2,3].includes(Mi(e.constraint_json).account.kickback_type||0)?(v(),h("div",oa,[sa,g("span",_a,z(Qi(Mi(e.constraint_json).account.kickback_value||"{}","fixed"))+"元 ",1)])):U("",!0),g("div",ra,[pa,g("span",ka,z(Hi(Mi(e.constraint_json).account.member_kickback_type||0)),1)]),[1,3].includes(Mi(e.constraint_json).account.member_kickback_type||0)?(v(),h("div",da,[ua,g("span",ma,z(Qi(Mi(e.constraint_json).account.member_kickback_value||"{}","point"))+"% ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).account.member_kickback_type||0)?(v(),h("div",ba,[fa,g("span",xa,z((Mi(e.constraint_json).account.month_fee*Qi(Mi(e.constraint_json).account.member_kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).account.member_kickback_type||0)?(v(),h("div",ya,[va,g("span",ha,z((Mi(e.constraint_json).account.year_fee*Qi(Mi(e.constraint_json).account.member_kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[2,3].includes(Mi(e.constraint_json).account.member_kickback_type||0)?(v(),h("div",ga,[Va,g("span",ja,z(Qi(Mi(e.constraint_json).account.member_kickback_value||"{}","fixed"))+"元 ",1)])):U("",!0)])):U("",!0),Mi(e.constraint_json).kongfz?(v(),h("div",wa,[za,g("div",Ua,[Ca,g("span",Sa,z(Mi(e.constraint_json).kongfz.month_fee||0)+"元 ",1)]),g("div",Na,[Ja,g("span",Oa,z(Mi(e.constraint_json).kongfz.year_fee||0)+"元 ",1)]),g("div",Fa,[Aa,g("span",Ta,z(Mi(e.constraint_json).kongfz.cost||0)+"元 ",1)]),g("div",qa,[Ia,g("span",Pa,z(Hi(Mi(e.constraint_json).kongfz.kickback_type||0)),1)]),[1,3].includes(Mi(e.constraint_json).kongfz.kickback_type||0)?(v(),h("div",Ea,[Ra,g("span",Ba,z(Qi(Mi(e.constraint_json).kongfz.kickback_value||"{}","point"))+"% ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).kongfz.kickback_type||0)?(v(),h("div",Ka,[$a,g("span",Da,z((Mi(e.constraint_json).kongfz.month_fee*Qi(Mi(e.constraint_json).kongfz.kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).kongfz.kickback_type||0)?(v(),h("div",La,[Za,g("span",Ga,z((Mi(e.constraint_json).kongfz.year_fee*Qi(Mi(e.constraint_json).kongfz.kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[2,3].includes(Mi(e.constraint_json).kongfz.kickback_type||0)?(v(),h("div",Ha,[Ma,g("span",Qa,z(Qi(Mi(e.constraint_json).kongfz.kickback_value||"{}","fixed"))+"元 ",1)])):U("",!0),g("div",Wa,[Xa,g("span",Ya,z(Hi(Mi(e.constraint_json).kongfz.member_kickback_type||0)),1)]),[1,3].includes(Mi(e.constraint_json).kongfz.member_kickback_type||0)?(v(),h("div",en,[an,g("span",nn,z(Qi(Mi(e.constraint_json).kongfz.member_kickback_value||"{}","point"))+"% ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).kongfz.member_kickback_type||0)?(v(),h("div",cn,[tn,g("span",ln,z((Mi(e.constraint_json).kongfz.month_fee*Qi(Mi(e.constraint_json).kongfz.member_kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).kongfz.member_kickback_type||0)?(v(),h("div",on,[sn,g("span",_n,z((Mi(e.constraint_json).kongfz.year_fee*Qi(Mi(e.constraint_json).kongfz.member_kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[2,3].includes(Mi(e.constraint_json).kongfz.member_kickback_type||0)?(v(),h("div",rn,[pn,g("span",kn,z(Qi(Mi(e.constraint_json).kongfz.member_kickback_value||"{}","fixed"))+"元 ",1)])):U("",!0)])):U("",!0),Mi(e.constraint_json).pdd?(v(),h("div",dn,[un,g("div",mn,[bn,g("span",fn,z(Mi(e.constraint_json).pdd.month_fee||0)+"元 ",1)]),g("div",xn,[yn,g("span",vn,z(Mi(e.constraint_json).pdd.year_fee||0)+"元 ",1)]),g("div",hn,[gn,g("span",Vn,z(Mi(e.constraint_json).pdd.cost||0)+"元 ",1)]),g("div",jn,[wn,g("span",zn,z(Hi(Mi(e.constraint_json).pdd.kickback_type||0)),1)]),[1,3].includes(Mi(e.constraint_json).pdd.kickback_type||0)?(v(),h("div",Un,[Cn,g("span",Sn,z(Qi(Mi(e.constraint_json).pdd.kickback_value||"{}","point"))+"% ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).pdd.kickback_type||0)?(v(),h("div",Nn,[Jn,g("span",On,z((Mi(e.constraint_json).pdd.month_fee*Qi(Mi(e.constraint_json).pdd.kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).pdd.kickback_type||0)?(v(),h("div",Fn,[An,g("span",Tn,z((Mi(e.constraint_json).pdd.year_fee*Qi(Mi(e.constraint_json).pdd.kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[2,3].includes(Mi(e.constraint_json).pdd.kickback_type||0)?(v(),h("div",qn,[In,g("span",Pn,z(Qi(Mi(e.constraint_json).pdd.kickback_value||"{}","fixed"))+"元 ",1)])):U("",!0),g("div",En,[Rn,g("span",Bn,z(Hi(Mi(e.constraint_json).pdd.member_kickback_type||0)),1)]),[1,3].includes(Mi(e.constraint_json).pdd.member_kickback_type||0)?(v(),h("div",Kn,[$n,g("span",Dn,z(Qi(Mi(e.constraint_json).pdd.member_kickback_value||"{}","point"))+"% ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).pdd.member_kickback_type||0)?(v(),h("div",Ln,[Zn,g("span",Gn,z((Mi(e.constraint_json).pdd.month_fee*Qi(Mi(e.constraint_json).pdd.member_kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).pdd.member_kickback_type||0)?(v(),h("div",Hn,[Mn,g("span",Qn,z((Mi(e.constraint_json).pdd.year_fee*Qi(Mi(e.constraint_json).pdd.member_kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[2,3].includes(Mi(e.constraint_json).pdd.member_kickback_type||0)?(v(),h("div",Wn,[Xn,g("span",Yn,z(Qi(Mi(e.constraint_json).pdd.member_kickback_value||"{}","fixed"))+"元 ",1)])):U("",!0)])):U("",!0),Mi(e.constraint_json).resource_tier?(v(),h("div",ec,[ac,g("div",nc,[cc,g("span",tc,z(Mi(e.constraint_json).resource_tier.tier1_fee||0)+"元/月 ",1)]),g("div",ic,[lc,g("span",oc,z(Mi(e.constraint_json).resource_tier.tier2_fee||50)+"元/月 ",1)]),g("div",sc,[_c,g("span",rc,z(Mi(e.constraint_json).resource_tier.tier2_cost||0)+"元/月 ",1)]),g("div",pc,[kc,g("span",dc,z(Mi(e.constraint_json).resource_tier.tier3_fee||100)+"元/月 ",1)]),g("div",uc,[mc,g("span",bc,z(Mi(e.constraint_json).resource_tier.tier3_cost||0)+"元/月 ",1)]),g("div",fc,[xc,g("span",yc,z(Mi(e.constraint_json).resource_tier.tier4_fee||200)+"元/月 ",1)]),g("div",vc,[hc,g("span",gc,z(Mi(e.constraint_json).resource_tier.tier4_cost||0)+"元/月 ",1)]),g("div",Vc,[jc,g("span",wc,z(Mi(e.constraint_json).resource_tier.tier5_fee||300)+"元/月 ",1)]),g("div",zc,[Uc,g("span",Cc,z(Mi(e.constraint_json).resource_tier.cost||0)+"元 ",1)]),g("div",Sc,[Nc,g("span",Jc,z(Hi(Mi(e.constraint_json).resource_tier.kickback_type||0)),1)]),[1,3].includes(Mi(e.constraint_json).resource_tier.kickback_type||0)?(v(),h("div",Oc,[Fc,g("span",Ac,z(Qi(Mi(e.constraint_json).resource_tier.kickback_value||"{}","point"))+"% ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).resource_tier.kickback_type||0)?(v(),h("div",Tc,[qc,g("span",Ic,z((Mi(e.constraint_json).resource_tier.tier1_fee*Qi(Mi(e.constraint_json).resource_tier.kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).resource_tier.kickback_type||0)?(v(),h("div",Pc,[Ec,g("span",Rc,z((Mi(e.constraint_json).resource_tier.tier2_fee*Qi(Mi(e.constraint_json).resource_tier.kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[2,3].includes(Mi(e.constraint_json).resource_tier.kickback_type||0)?(v(),h("div",Bc,[Kc,g("span",$c,z(Qi(Mi(e.constraint_json).resource_tier.kickback_value||"{}","fixed"))+"元 ",1)])):U("",!0),g("div",Dc,[Lc,g("span",Zc,z(Hi(Mi(e.constraint_json).resource_tier.member_kickback_type||0)),1)]),[1,3].includes(Mi(e.constraint_json).resource_tier.member_kickback_type||0)?(v(),h("div",Gc,[Hc,g("span",Mc,z(Qi(Mi(e.constraint_json).resource_tier.member_kickback_value||"{}","point"))+"% ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).resource_tier.member_kickback_type||0)?(v(),h("div",Qc,[Wc,g("span",Xc,z((Mi(e.constraint_json).resource_tier.tier1_fee*Qi(Mi(e.constraint_json).resource_tier.member_kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[1,3].includes(Mi(e.constraint_json).resource_tier.member_kickback_type||0)?(v(),h("div",Yc,[et,g("span",at,z((Mi(e.constraint_json).resource_tier.tier2_fee*Qi(Mi(e.constraint_json).resource_tier.member_kickback_value||"{}","point")/100).toFixed(2))+"元 ",1)])):U("",!0),[2,3].includes(Mi(e.constraint_json).resource_tier.member_kickback_type||0)?(v(),h("div",nt,[ct,g("span",tt,z(Qi(Mi(e.constraint_json).resource_tier.member_kickback_value||"{}","fixed"))+"元 ",1)])):U("",!0)])):U("",!0),Mi(e.constraint_json).transaction?(v(),h("div",it,[lt,g("div",ot,[st,g("span",_t,z(Mi(e.constraint_json).transaction.same_warehouse_shop||0)+"元 ",1)]),g("div",rt,[pt,g("span",kt,z(Mi(e.constraint_json).transaction.cost||0)+"元 ",1)]),g("div",dt,[ut,g("span",mt,z(Hi(Mi(e.constraint_json).transaction.kickback_type||0)),1)])])):U("",!0),Mi(e.constraint_json).different_shop?(v(),h("div",bt,[ft,g("div",xt,[yt,g("span",vt,z(Mi(e.constraint_json).different_shop.warehouse_percent||0)+"% + "+z(Mi(e.constraint_json).different_shop.warehouse_fixed||.02)+"元 ",1)]),g("div",ht,[gt,g("span",Vt,z(Mi(e.constraint_json).different_shop.owner_percent||0)+"% + "+z(Mi(e.constraint_json).different_shop.owner_fixed||.02)+"元 ",1)]),g("div",jt,[wt,g("span",zt,z(Mi(e.constraint_json).different_shop.cost||0)+"元 ",1)]),g("div",Ut,[Ct,g("span",St,z(Hi(Mi(e.constraint_json).different_shop.kickback_type||0)),1)])])):U("",!0)])):(v(),h("div",Nt,Jt))])):(v(),h("div",Ot,Ft))])),_:1}),V(q,{type:"selection",align:"center",width:"55"}),V(q,{prop:"title",align:"center",label:"标题",width:"120"}),V(q,{prop:"settled_cost_key",align:"center",label:"入驻标识",width:"180"}),V(q,{align:"center",label:"小程序佣金",width:"180"},{default:j((({row:e})=>[Mi(e.constraint_json)&&Mi(e.constraint_json).miniapp?(v(),h("div",At,[g("div",null," 类型: "+z(Hi(Mi(e.constraint_json).miniapp.kickback_type||0)),1),[1,3].includes(Mi(e.constraint_json).miniapp.kickback_type||0)?(v(),h("div",Tt," 提点: "+z(Qi(Mi(e.constraint_json).miniapp.kickback_value||"{}","point"))+"% ",1)):U("",!0),[2,3].includes(Mi(e.constraint_json).miniapp.kickback_type||0)?(v(),h("div",qt," 固费: "+z(Qi(Mi(e.constraint_json).miniapp.kickback_value||"{}","fixed"))+"元 ",1)):U("",!0)])):(v(),h("span",It,"-"))])),_:1}),V(q,{label:"操作",align:"center",fixed:"right"},{default:j((({row:e})=>[V(I,null,{default:j((()=>[V(t,{type:"primary",size:"small",onClick:a=>(e=>{var a,n,c,t,i,l,o,s,_,r,p,k,d,u,m,b,f,x,y,v,h,g,V,j,w,z,U,C,S,N,J,O,F,A,T,q,I,P,E,R,B,K,$,D,L,Z,G,H,M,Q,W,X,Y,ee,ae,ne,ce,te,ie,le,oe,se,_e,re,pe,ke,de,ue,me,be,fe;il.value="edit",jl();let xe=0,ye=0;const ve="string"==typeof e.constraint_json?JSON.parse(e.constraint_json):e.constraint_json;if(ve){if(xe=null!=(a=ve.books_count_min)?a:0,ye=null!=(n=ve.books_count_max)?n:0,ve.miniapp){ol.miniapp_month_fee=null!=(c=ve.miniapp.month_fee)?c:0,ol.miniapp_cost=null!=(t=ve.miniapp.cost)?t:0,ol.miniapp_year_fee=null!=(i=ve.miniapp.year_fee)?i:0,ol.miniapp_kickback_type=null!=(l=ve.miniapp.kickback_type)?l:0;try{const e=JSON.parse(ve.miniapp.kickback_value||"{}");ol.miniapp_kickback_point=null!=(o=e.point)?o:0,ol.miniapp_kickback_fixed=null!=(s=e.fixed)?s:0}catch(je){console.error("解析小程序佣金值失败:",je)}ol.miniapp_member_kickback_type=null!=(_=ve.miniapp.member_kickback_type)?_:0;try{const e=JSON.parse(ve.miniapp.member_kickback_value||"{}");ol.miniapp_member_kickback_point=null!=(r=e.point)?r:0,ol.miniapp_member_kickback_fixed=null!=(p=e.fixed)?p:0}catch(je){console.error("解析小程序会员佣金值失败:",je)}}if(ve.account){ol.account_month_fee=null!=(k=ve.account.month_fee)?k:0,ol.account_cost=null!=(d=ve.account.cost)?d:0,ol.account_year_fee=null!=(u=ve.account.year_fee)?u:0,ol.account_kickback_type=null!=(m=ve.account.kickback_type)?m:0;try{const e=JSON.parse(ve.account.kickback_value||"{}");ol.account_kickback_point=null!=(b=e.point)?b:0,ol.account_kickback_fixed=null!=(f=e.fixed)?f:0}catch(je){console.error("解析账号佣金值失败:",je)}ol.account_member_kickback_type=null!=(x=ve.account.member_kickback_type)?x:0;try{const e=JSON.parse(ve.account.member_kickback_value||"{}");ol.account_member_kickback_point=null!=(y=e.point)?y:0,ol.account_member_kickback_fixed=null!=(v=e.fixed)?v:0}catch(je){console.error("解析账号会员佣金值失败:",je)}}if(ve.kongfz){ol.kongfz_month_fee=null!=(h=ve.kongfz.month_fee)?h:0,ol.kongfz_cost=null!=(g=ve.kongfz.cost)?g:0,ol.kongfz_year_fee=null!=(V=ve.kongfz.year_fee)?V:0,ol.kongfz_kickback_type=null!=(j=ve.kongfz.kickback_type)?j:0;try{const e=JSON.parse(ve.kongfz.kickback_value||"{}");ol.kongfz_kickback_point=null!=(w=e.point)?w:0,ol.kongfz_kickback_fixed=null!=(z=e.fixed)?z:0}catch(je){console.error("解析孔夫子店铺佣金值失败:",je)}ol.kongfz_member_kickback_type=null!=(U=ve.kongfz.member_kickback_type)?U:0;try{const e=JSON.parse(ve.kongfz.member_kickback_value||"{}");ol.kongfz_member_kickback_point=null!=(C=e.point)?C:0,ol.kongfz_member_kickback_fixed=null!=(S=e.fixed)?S:0}catch(je){console.error("解析孔夫子店铺会员佣金值失败:",je)}}if(ve.pdd){ol.pdd_month_fee=null!=(N=ve.pdd.month_fee)?N:0,ol.pdd_cost=null!=(J=ve.pdd.cost)?J:0,ol.pdd_year_fee=null!=(O=ve.pdd.year_fee)?O:0,ol.pdd_kickback_type=null!=(F=ve.pdd.kickback_type)?F:0;try{const e=JSON.parse(ve.pdd.kickback_value||"{}");ol.pdd_kickback_point=null!=(A=e.point)?A:0,ol.pdd_kickback_fixed=null!=(T=e.fixed)?T:0}catch(je){console.error("解析拼多多专营店佣金值失败:",je)}ol.pdd_member_kickback_type=null!=(q=ve.pdd.member_kickback_type)?q:0;try{const e=JSON.parse(ve.pdd.member_kickback_value||"{}");ol.pdd_member_kickback_point=null!=(I=e.point)?I:0,ol.pdd_member_kickback_fixed=null!=(P=e.fixed)?P:0}catch(je){console.error("解析拼多多专营店会员佣金值失败:",je)}}if(ve.resource_tier){ol.resource_tier1_fee=null!=(E=ve.resource_tier.tier1_fee)?E:0,ol.resource_tier2_fee=null!=(R=ve.resource_tier.tier2_fee)?R:50,ol.resource_tier3_fee=null!=(B=ve.resource_tier.tier3_fee)?B:100,ol.resource_tier4_fee=null!=(K=ve.resource_tier.tier4_fee)?K:200,ol.resource_tier5_fee=null!=($=ve.resource_tier.tier5_fee)?$:300,ol.resource_tier_cost=null!=(D=ve.resource_tier.cost)?D:0,ol.resource_tier_kickback_type=null!=(L=ve.resource_tier.kickback_type)?L:0;try{const e=JSON.parse(ve.resource_tier.kickback_value||"{}");ol.resource_tier_kickback_point=null!=(Z=e.point)?Z:0,ol.resource_tier_kickback_fixed=null!=(G=e.fixed)?G:0}catch(je){console.error("解析资源费分级佣金值失败:",je)}ol.resource_tier_member_kickback_type=null!=(H=ve.resource_tier.member_kickback_type)?H:0;try{const e=JSON.parse(ve.resource_tier.member_kickback_value||"{}");ol.resource_tier_member_kickback_point=null!=(M=e.point)?M:0,ol.resource_tier_member_kickback_fixed=null!=(Q=e.fixed)?Q:0}catch(je){console.error("解析资源费分级会员佣金值失败:",je)}}if(ve.transaction){ol.transaction_same_warehouse_shop=null!=(W=ve.transaction.same_warehouse_shop)?W:0,ol.transaction_cost=null!=(X=ve.transaction.cost)?X:0,ol.transaction_same_kickback_type=null!=(Y=ve.transaction.kickback_type)?Y:0;try{const e=JSON.parse(ve.transaction.kickback_value||"{}");ol.transaction_same_kickback_point=null!=(ee=e.point)?ee:0,ol.transaction_same_kickback_fixed=null!=(ae=e.fixed)?ae:0}catch(je){console.error("解析交易手续费佣金值失败:",je)}ol.transaction_same_member_kickback_type=null!=(ne=ve.transaction.member_kickback_type)?ne:0;try{const e=JSON.parse(ve.transaction.member_kickback_value||"{}");ol.transaction_same_member_kickback_point=null!=(ce=e.point)?ce:0,ol.transaction_same_member_kickback_fixed=null!=(te=e.fixed)?te:0}catch(je){console.error("解析交易手续费会员佣金值失败:",je)}}if(ve.different_shop){ol.different_shop_warehouse_percent=null!=(ie=ve.different_shop.warehouse_percent)?ie:0,ol.different_shop_warehouse_fixed=null!=(le=ve.different_shop.warehouse_fixed)?le:.02,ol.different_shop_owner_percent=null!=(oe=ve.different_shop.owner_percent)?oe:0,ol.different_shop_owner_fixed=null!=(se=ve.different_shop.owner_fixed)?se:.02,ol.different_shop_cost=null!=(_e=ve.different_shop.cost)?_e:0,ol.different_shop_kickback_type=null!=(re=ve.different_shop.kickback_type)?re:0;try{const e=JSON.parse(ve.different_shop.kickback_value||"{}");ol.different_shop_kickback_point=null!=(pe=e.point)?pe:0,ol.different_shop_kickback_fixed=null!=(ke=e.fixed)?ke:0}catch(je){console.error("解析同库房不同店铺佣金值失败:",je)}ol.different_shop_member_kickback_type=null!=(de=ve.different_shop.member_kickback_type)?de:0;try{const e=JSON.parse(ve.different_shop.member_kickback_value||"{}");ol.different_shop_member_kickback_point=null!=(ue=e.point)?ue:0,ol.different_shop_member_kickback_fixed=null!=(me=e.fixed)?me:0}catch(je){console.error("解析同库房不同店铺会员佣金值失败:",je)}}}let he=0,ge=0;const Ve="string"==typeof e.kickback_value?JSON.parse(e.kickback_value):e.kickback_value;Ve?(he=null!=(be=Ve.point)?be:0,ge=null!=(fe=Ve.fixed)?fe:0):(he=1===e.kickback_type?Number(e.kickback_value):0,ge=2===e.kickback_type?Number(e.kickback_value):0),Object.assign(ol,{id:e.id,title:e.title,settled_cost_key:e.settled_cost_key,member_type:ve.member_type||1,constraint_json:ve,books_count_min:xe,books_count_max:ye,kickback_type:e.kickback_type,kickback_value:"string"==typeof Ve?Ve:JSON.stringify(Ve),kickback_point:he,kickback_fixed:ge,resource_cost_type:e.resource_cost_type,resource_cost_value:e.resource_cost_value,service_rate:e.service_rate,price:e.price,state:e.state,note:e.note}),tl.value=!0})(e)},{default:j((()=>[w("编辑")])),_:2},1032,["onClick"]),V(t,{type:"danger",size:"small",onClick:a=>(async e=>{try{await A(e.id),S.success("删除成功"),await Wi()}catch(a){console.error("删除失败:",a),S.error("删除失败")}})(e),disabled:1===e.is_del},{default:j((()=>[w(" 删除 ")])),_:2},1032,["onClick","disabled"])])),_:2},1024)])),_:1}),V(q,{align:"center",label:"孔夫子佣金",width:"180"},{default:j((({row:e})=>[Mi(e.constraint_json)&&Mi(e.constraint_json).kongfz?(v(),h("div",Pt,[g("div",null," 类型: "+z(Hi(Mi(e.constraint_json).kongfz.kickback_type||0)),1),[1,3].includes(Mi(e.constraint_json).kongfz.kickback_type||0)?(v(),h("div",Et," 提点: "+z(Qi(Mi(e.constraint_json).kongfz.kickback_value||"{}","point"))+"% ",1)):U("",!0),[2,3].includes(Mi(e.constraint_json).kongfz.kickback_type||0)?(v(),h("div",Rt," 固费: "+z(Qi(Mi(e.constraint_json).kongfz.kickback_value||"{}","fixed"))+"元 ",1)):U("",!0)])):(v(),h("span",Bt,"-"))])),_:1}),V(q,{align:"center",label:"资源费佣金",width:"180"},{default:j((({row:e})=>[Mi(e.constraint_json)&&Mi(e.constraint_json).resource_tier?(v(),h("div",Kt,[g("div",null," 类型: "+z(Hi(Mi(e.constraint_json).resource_tier.kickback_type||0)),1),[1,3].includes(Mi(e.constraint_json).resource_tier.kickback_type||0)?(v(),h("div",$t," 提点: "+z(Qi(Mi(e.constraint_json).resource_tier.kickback_value||"{}","point"))+"% ",1)):U("",!0),[2,3].includes(Mi(e.constraint_json).resource_tier.kickback_type||0)?(v(),h("div",Dt," 固费: "+z(Qi(Mi(e.constraint_json).resource_tier.kickback_value||"{}","fixed"))+"元 ",1)):U("",!0)])):(v(),h("span",Lt,"-"))])),_:1}),V(q,{prop:"price",align:"center",label:"价格(元)",width:"120"}),V(q,{prop:"state",align:"center",label:"状态",width:"80"},{default:j((({row:e})=>[V(P,{type:1===e.state?"success":"danger"},{default:j((()=>[w(z(1===e.state?"正常":"失效"),1)])),_:2},1032,["type"])])),_:1}),V(q,{prop:"note",align:"center",label:"备注",width:"200"}),V(q,{prop:"is_del",align:"center",label:"删除状态",width:"100"},{default:j((({row:e})=>[w(z(0===e.is_del?"正常":"已删除"),1)])),_:1})])),_:1},8,["data"]),g("div",Zt,[V(Sl,{"current-page":O.current,"onUpdate:currentPage":a[2]||(a[2]=e=>O.current=e),"page-size":O.size,"onUpdate:pageSize":a[3]||(a[3]=e=>O.size=e),total:O.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:el,onCurrentChange:al},null,8,["current-page","page-size","total"])]),V(Tl,{modelValue:tl.value,"onUpdate:modelValue":a[80]||(a[80]=e=>tl.value=e),title:"add"===il.value?"新增配置":"编辑配置",width:"50%","close-on-click-modal":!1},{footer:j((()=>[g("span",Zi,[V(t,{onClick:a[79]||(a[79]=e=>tl.value=!1)},{default:j((()=>[w("取消")])),_:1}),V(t,{type:"primary",onClick:Ul,loading:zl.value},{default:j((()=>[w("确定")])),_:1},8,["loading"])])])),default:j((()=>[V(T,{ref_key:"formRef",ref:ll,model:ol,rules:sl,"label-width":"120px","label-position":"right"},{default:j((()=>[V(c,{label:"标题",prop:"title"},{default:j((()=>[V(n,{modelValue:ol.title,"onUpdate:modelValue":a[4]||(a[4]=e=>ol.title=e),placeholder:"请输入标题"},null,8,["modelValue"])])),_:1}),V(c,{label:"入驻标识",prop:"settled_cost_key"},{default:j((()=>[V(n,{modelValue:ol.settled_cost_key,"onUpdate:modelValue":a[5]||(a[5]=e=>ol.settled_cost_key=e),placeholder:"请输入入驻标识",disabled:"edit"===il.value},null,8,["modelValue","disabled"])])),_:1}),V(c,{label:"会员类型",prop:"member_type"},{default:j((()=>[V(Jl,{modelValue:ol.member_type,"onUpdate:modelValue":a[6]||(a[6]=e=>ol.member_type=e),placeholder:"请选择会员类型",clearable:""},{default:j((()=>[V(Nl,{label:"小程序",value:1})])),_:1},8,["modelValue"])])),_:1}),V(c,{label:"最小本数",prop:"books_count_min"},{default:j((()=>[V(Ol,{modelValue:ol.books_count_min,"onUpdate:modelValue":a[7]||(a[7]=e=>ol.books_count_min=e),min:0,precision:0,placeholder:"请输入最小本数"},null,8,["modelValue"])])),_:1}),V(c,{label:"最大本数",prop:"books_count_max"},{default:j((()=>[V(Ol,{modelValue:ol.books_count_max,"onUpdate:modelValue":a[8]||(a[8]=e=>ol.books_count_max=e),min:0,precision:0,placeholder:"请输入最大本数"},null,8,["modelValue"])])),_:1}),V(Fl,{"content-position":"left"},{default:j((()=>[w("小程序收费")])),_:1}),V(c,{label:"月收费",prop:"miniapp_month_fee"},{default:j((()=>[V(Ol,{modelValue:ol.miniapp_month_fee,"onUpdate:modelValue":a[9]||(a[9]=e=>ol.miniapp_month_fee=e),min:0,precision:2,placeholder:"请输入月收费"},null,8,["modelValue"]),Gt])),_:1}),V(c,{label:"成本",prop:"miniapp_cost"},{default:j((()=>[V(Ol,{modelValue:ol.miniapp_cost,"onUpdate:modelValue":a[10]||(a[10]=e=>ol.miniapp_cost=e),min:0,precision:2,placeholder:"请输入成本"},null,8,["modelValue"]),Ht])),_:1}),V(c,{label:"年收费",prop:"miniapp_year_fee"},{default:j((()=>[V(Ol,{modelValue:ol.miniapp_year_fee,"onUpdate:modelValue":a[11]||(a[11]=e=>ol.miniapp_year_fee=e),min:0,precision:2,placeholder:"请输入年收费"},null,8,["modelValue"]),Mt])),_:1}),V(c,{label:"推广员佣金类型",prop:"miniapp_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.miniapp_kickback_type,"onUpdate:modelValue":a[12]||(a[12]=e=>ol.miniapp_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.miniapp_kickback_type)?(v(),C(c,{key:0,label:"提点比例",prop:"miniapp_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.miniapp_kickback_point,"onUpdate:modelValue":a[13]||(a[13]=e=>ol.miniapp_kickback_point=e),min:0,max:100,precision:2,onChange:rl},null,8,["modelValue"]),Qt])),_:1})):U("",!0),[2,3].includes(ol.miniapp_kickback_type)?(v(),C(c,{key:1,label:"固定费用",prop:"miniapp_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.miniapp_kickback_fixed,"onUpdate:modelValue":a[14]||(a[14]=e=>ol.miniapp_kickback_fixed=e),min:0,precision:2,onChange:rl},null,8,["modelValue"]),Wt])),_:1})):U("",!0),V(c,{label:"会员佣金类型",prop:"miniapp_member_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.miniapp_member_kickback_type,"onUpdate:modelValue":a[15]||(a[15]=e=>ol.miniapp_member_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.miniapp_member_kickback_type)?(v(),C(c,{key:2,label:"提点比例",prop:"miniapp_member_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.miniapp_member_kickback_point,"onUpdate:modelValue":a[16]||(a[16]=e=>ol.miniapp_member_kickback_point=e),min:0,max:100,precision:2,onChange:pl},null,8,["modelValue"]),Xt])),_:1})):U("",!0),[2,3].includes(ol.miniapp_member_kickback_type)?(v(),C(c,{key:3,label:"固定费用",prop:"miniapp_member_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.miniapp_member_kickback_fixed,"onUpdate:modelValue":a[17]||(a[17]=e=>ol.miniapp_member_kickback_fixed=e),min:0,precision:2,onChange:pl},null,8,["modelValue"]),Yt])),_:1})):U("",!0),V(Fl,{"content-position":"left"},{default:j((()=>[w("小程序新增账号收费")])),_:1}),V(c,{label:"月收费",prop:"account_month_fee"},{default:j((()=>[V(Ol,{modelValue:ol.account_month_fee,"onUpdate:modelValue":a[18]||(a[18]=e=>ol.account_month_fee=e),min:0,precision:2,placeholder:"请输入月收费"},null,8,["modelValue"]),ei])),_:1}),V(c,{label:"成本",prop:"account_cost"},{default:j((()=>[V(Ol,{modelValue:ol.account_cost,"onUpdate:modelValue":a[19]||(a[19]=e=>ol.account_cost=e),min:0,precision:2,placeholder:"请输入成本"},null,8,["modelValue"]),ai])),_:1}),V(c,{label:"年收费",prop:"account_year_fee"},{default:j((()=>[V(Ol,{modelValue:ol.account_year_fee,"onUpdate:modelValue":a[20]||(a[20]=e=>ol.account_year_fee=e),min:0,precision:2,placeholder:"请输入年收费"},null,8,["modelValue"]),ni])),_:1}),V(c,{label:"推广员佣金类型",prop:"account_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.account_kickback_type,"onUpdate:modelValue":a[21]||(a[21]=e=>ol.account_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.account_kickback_type)?(v(),C(c,{key:4,label:"提点比例",prop:"account_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.account_kickback_point,"onUpdate:modelValue":a[22]||(a[22]=e=>ol.account_kickback_point=e),min:0,max:100,precision:2,onChange:kl},null,8,["modelValue"]),ci])),_:1})):U("",!0),[2,3].includes(ol.account_kickback_type)?(v(),C(c,{key:5,label:"固定费用",prop:"account_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.account_kickback_fixed,"onUpdate:modelValue":a[23]||(a[23]=e=>ol.account_kickback_fixed=e),min:0,precision:2,onChange:kl},null,8,["modelValue"]),ti])),_:1})):U("",!0),V(c,{label:"会员佣金类型",prop:"account_member_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.account_member_kickback_type,"onUpdate:modelValue":a[24]||(a[24]=e=>ol.account_member_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.account_member_kickback_type)?(v(),C(c,{key:6,label:"提点比例",prop:"account_member_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.account_member_kickback_point,"onUpdate:modelValue":a[25]||(a[25]=e=>ol.account_member_kickback_point=e),min:0,max:100,precision:2,onChange:dl},null,8,["modelValue"]),ii])),_:1})):U("",!0),[2,3].includes(ol.account_member_kickback_type)?(v(),C(c,{key:7,label:"固定费用",prop:"account_member_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.account_member_kickback_fixed,"onUpdate:modelValue":a[26]||(a[26]=e=>ol.account_member_kickback_fixed=e),min:0,precision:2,onChange:dl},null,8,["modelValue"]),li])),_:1})):U("",!0),V(Fl,{"content-position":"left"},{default:j((()=>[w("孔夫子店铺收费")])),_:1}),V(c,{label:"月收费",prop:"kongfz_month_fee"},{default:j((()=>[V(Ol,{modelValue:ol.kongfz_month_fee,"onUpdate:modelValue":a[27]||(a[27]=e=>ol.kongfz_month_fee=e),min:0,precision:2,placeholder:"请输入月收费"},null,8,["modelValue"]),oi])),_:1}),V(c,{label:"成本",prop:"kongfz_cost"},{default:j((()=>[V(Ol,{modelValue:ol.kongfz_cost,"onUpdate:modelValue":a[28]||(a[28]=e=>ol.kongfz_cost=e),min:0,precision:2,placeholder:"请输入成本"},null,8,["modelValue"]),si])),_:1}),V(c,{label:"年收费",prop:"kongfz_year_fee"},{default:j((()=>[V(Ol,{modelValue:ol.kongfz_year_fee,"onUpdate:modelValue":a[29]||(a[29]=e=>ol.kongfz_year_fee=e),min:0,precision:2,placeholder:"请输入年收费"},null,8,["modelValue"]),_i])),_:1}),V(c,{label:"推广员佣金类型",prop:"kongfz_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.kongfz_kickback_type,"onUpdate:modelValue":a[30]||(a[30]=e=>ol.kongfz_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.kongfz_kickback_type)?(v(),C(c,{key:8,label:"提点比例",prop:"kongfz_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.kongfz_kickback_point,"onUpdate:modelValue":a[31]||(a[31]=e=>ol.kongfz_kickback_point=e),min:0,max:100,precision:2,onChange:ul},null,8,["modelValue"]),ri])),_:1})):U("",!0),[2,3].includes(ol.kongfz_kickback_type)?(v(),C(c,{key:9,label:"固定费用",prop:"kongfz_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.kongfz_kickback_fixed,"onUpdate:modelValue":a[32]||(a[32]=e=>ol.kongfz_kickback_fixed=e),min:0,precision:2,onChange:ul},null,8,["modelValue"]),pi])),_:1})):U("",!0),V(c,{label:"会员佣金类型",prop:"kongfz_member_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.kongfz_member_kickback_type,"onUpdate:modelValue":a[33]||(a[33]=e=>ol.kongfz_member_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.kongfz_member_kickback_type)?(v(),C(c,{key:10,label:"提点比例",prop:"kongfz_member_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.kongfz_member_kickback_point,"onUpdate:modelValue":a[34]||(a[34]=e=>ol.kongfz_member_kickback_point=e),min:0,max:100,precision:2,onChange:fl},null,8,["modelValue"]),ki])),_:1})):U("",!0),[2,3].includes(ol.kongfz_member_kickback_type)?(v(),C(c,{key:11,label:"固定费用",prop:"kongfz_member_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.kongfz_member_kickback_fixed,"onUpdate:modelValue":a[35]||(a[35]=e=>ol.kongfz_member_kickback_fixed=e),min:0,precision:2,onChange:fl},null,8,["modelValue"]),di])),_:1})):U("",!0),V(Fl,{"content-position":"left"},{default:j((()=>[w("拼多多专营店收费")])),_:1}),V(c,{label:"月收费",prop:"pdd_month_fee"},{default:j((()=>[V(Ol,{modelValue:ol.pdd_month_fee,"onUpdate:modelValue":a[36]||(a[36]=e=>ol.pdd_month_fee=e),min:0,precision:2,placeholder:"请输入月收费"},null,8,["modelValue"]),ui])),_:1}),V(c,{label:"成本",prop:"pdd_cost"},{default:j((()=>[V(Ol,{modelValue:ol.pdd_cost,"onUpdate:modelValue":a[37]||(a[37]=e=>ol.pdd_cost=e),min:0,precision:2,placeholder:"请输入成本"},null,8,["modelValue"]),mi])),_:1}),V(c,{label:"年收费",prop:"pdd_year_fee"},{default:j((()=>[V(Ol,{modelValue:ol.pdd_year_fee,"onUpdate:modelValue":a[38]||(a[38]=e=>ol.pdd_year_fee=e),min:0,precision:2,placeholder:"请输入年收费"},null,8,["modelValue"]),bi])),_:1}),V(c,{label:"推广员佣金类型",prop:"pdd_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.pdd_kickback_type,"onUpdate:modelValue":a[39]||(a[39]=e=>ol.pdd_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.pdd_kickback_type)?(v(),C(c,{key:12,label:"提点比例",prop:"pdd_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.pdd_kickback_point,"onUpdate:modelValue":a[40]||(a[40]=e=>ol.pdd_kickback_point=e),min:0,max:100,precision:2,onChange:ml},null,8,["modelValue"]),fi])),_:1})):U("",!0),[2,3].includes(ol.pdd_kickback_type)?(v(),C(c,{key:13,label:"固定费用",prop:"pdd_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.pdd_kickback_fixed,"onUpdate:modelValue":a[41]||(a[41]=e=>ol.pdd_kickback_fixed=e),min:0,precision:2,onChange:ml},null,8,["modelValue"]),xi])),_:1})):U("",!0),V(c,{label:"会员佣金类型",prop:"pdd_member_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.pdd_member_kickback_type,"onUpdate:modelValue":a[42]||(a[42]=e=>ol.pdd_member_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.pdd_member_kickback_type)?(v(),C(c,{key:14,label:"提点比例",prop:"pdd_member_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.pdd_member_kickback_point,"onUpdate:modelValue":a[43]||(a[43]=e=>ol.pdd_member_kickback_point=e),min:0,max:100,precision:2,onChange:bl},null,8,["modelValue"]),yi])),_:1})):U("",!0),[2,3].includes(ol.pdd_member_kickback_type)?(v(),C(c,{key:15,label:"固定费用",prop:"pdd_member_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.pdd_member_kickback_fixed,"onUpdate:modelValue":a[44]||(a[44]=e=>ol.pdd_member_kickback_fixed=e),min:0,precision:2,onChange:bl},null,8,["modelValue"]),vi])),_:1})):U("",!0),V(Fl,{"content-position":"left"},{default:j((()=>[w("资源费分级收费")])),_:1}),V(c,{label:"3000条以下",prop:"resource_tier1_fee"},{default:j((()=>[V(Ol,{modelValue:ol.resource_tier1_fee,"onUpdate:modelValue":a[45]||(a[45]=e=>ol.resource_tier1_fee=e),min:0,precision:2,placeholder:"请输入月费用"},null,8,["modelValue"]),hi])),_:1}),V(c,{label:"3000-20000条",prop:"resource_tier2_fee"},{default:j((()=>[V(Ol,{modelValue:ol.resource_tier2_fee,"onUpdate:modelValue":a[46]||(a[46]=e=>ol.resource_tier2_fee=e),min:0,precision:2,placeholder:"请输入月费用"},null,8,["modelValue"]),gi])),_:1}),V(c,{label:"20000-50000条",prop:"resource_tier3_fee"},{default:j((()=>[V(Ol,{modelValue:ol.resource_tier3_fee,"onUpdate:modelValue":a[47]||(a[47]=e=>ol.resource_tier3_fee=e),min:0,precision:2,placeholder:"请输入月费用"},null,8,["modelValue"]),Vi])),_:1}),V(c,{label:"50000-100000条",prop:"resource_tier4_fee"},{default:j((()=>[V(Ol,{modelValue:ol.resource_tier4_fee,"onUpdate:modelValue":a[48]||(a[48]=e=>ol.resource_tier4_fee=e),min:0,precision:2,placeholder:"请输入月费用"},null,8,["modelValue"]),ji])),_:1}),V(c,{label:"100000条以上",prop:"resource_tier5_fee"},{default:j((()=>[V(Ol,{modelValue:ol.resource_tier5_fee,"onUpdate:modelValue":a[49]||(a[49]=e=>ol.resource_tier5_fee=e),min:0,precision:2,placeholder:"请输入月费用"},null,8,["modelValue"]),wi])),_:1}),V(c,{label:"成本",prop:"resource_tier_cost"},{default:j((()=>[V(Ol,{modelValue:ol.resource_tier_cost,"onUpdate:modelValue":a[50]||(a[50]=e=>ol.resource_tier_cost=e),min:0,precision:2,placeholder:"请输入成本"},null,8,["modelValue"]),zi])),_:1}),V(c,{label:"推广员佣金类型",prop:"resource_tier_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.resource_tier_kickback_type,"onUpdate:modelValue":a[51]||(a[51]=e=>ol.resource_tier_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.resource_tier_kickback_type)?(v(),C(c,{key:16,label:"提点比例",prop:"resource_tier_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.resource_tier_kickback_point,"onUpdate:modelValue":a[52]||(a[52]=e=>ol.resource_tier_kickback_point=e),min:0,max:100,precision:2,onChange:xl},null,8,["modelValue"]),Ui])),_:1})):U("",!0),[2,3].includes(ol.resource_tier_kickback_type)?(v(),C(c,{key:17,label:"固定费用",prop:"resource_tier_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.resource_tier_kickback_fixed,"onUpdate:modelValue":a[53]||(a[53]=e=>ol.resource_tier_kickback_fixed=e),min:0,precision:2,onChange:xl},null,8,["modelValue"]),Ci])),_:1})):U("",!0),V(c,{label:"会员佣金类型",prop:"resource_tier_member_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.resource_tier_member_kickback_type,"onUpdate:modelValue":a[54]||(a[54]=e=>ol.resource_tier_member_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.resource_tier_member_kickback_type)?(v(),C(c,{key:18,label:"提点比例",prop:"resource_tier_member_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.resource_tier_member_kickback_point,"onUpdate:modelValue":a[55]||(a[55]=e=>ol.resource_tier_member_kickback_point=e),min:0,max:100,precision:2,onChange:yl},null,8,["modelValue"]),Si])),_:1})):U("",!0),[2,3].includes(ol.resource_tier_member_kickback_type)?(v(),C(c,{key:19,label:"固定费用",prop:"resource_tier_member_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.resource_tier_member_kickback_fixed,"onUpdate:modelValue":a[56]||(a[56]=e=>ol.resource_tier_member_kickback_fixed=e),min:0,precision:2,onChange:yl},null,8,["modelValue"]),Ni])),_:1})):U("",!0),V(Fl,{"content-position":"left"},{default:j((()=>[w("交易手续费")])),_:1}),V(c,{label:"同库房同店铺",prop:"transaction_same_warehouse_shop"},{default:j((()=>[V(Ol,{modelValue:ol.transaction_same_warehouse_shop,"onUpdate:modelValue":a[57]||(a[57]=e=>ol.transaction_same_warehouse_shop=e),min:0,precision:2,placeholder:"请输入费用"},null,8,["modelValue"]),Ji])),_:1}),V(c,{label:"成本",prop:"transaction_cost"},{default:j((()=>[V(Ol,{modelValue:ol.transaction_cost,"onUpdate:modelValue":a[58]||(a[58]=e=>ol.transaction_cost=e),min:0,precision:2,placeholder:"请输入成本"},null,8,["modelValue"]),Oi])),_:1}),V(c,{label:"佣金类型",prop:"transaction_same_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.transaction_same_kickback_type,"onUpdate:modelValue":a[59]||(a[59]=e=>ol.transaction_same_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.transaction_same_kickback_type)?(v(),C(c,{key:20,label:"提点比例",prop:"transaction_same_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.transaction_same_kickback_point,"onUpdate:modelValue":a[60]||(a[60]=e=>ol.transaction_same_kickback_point=e),min:0,max:100,precision:2,onChange:vl},null,8,["modelValue"]),Fi])),_:1})):U("",!0),[2,3].includes(ol.transaction_same_kickback_type)?(v(),C(c,{key:21,label:"固定费用",prop:"transaction_same_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.transaction_same_kickback_fixed,"onUpdate:modelValue":a[61]||(a[61]=e=>ol.transaction_same_kickback_fixed=e),min:0,precision:2,onChange:vl},null,8,["modelValue"]),Ai])),_:1})):U("",!0),V(c,{label:"会员佣金类型",prop:"transaction_same_member_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.transaction_same_member_kickback_type,"onUpdate:modelValue":a[62]||(a[62]=e=>ol.transaction_same_member_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.transaction_same_member_kickback_type)?(v(),C(c,{key:22,label:"提点比例",prop:"transaction_same_member_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.transaction_same_member_kickback_point,"onUpdate:modelValue":a[63]||(a[63]=e=>ol.transaction_same_member_kickback_point=e),min:0,max:100,precision:2,onChange:hl},null,8,["modelValue"]),Ti])),_:1})):U("",!0),[2,3].includes(ol.transaction_same_member_kickback_type)?(v(),C(c,{key:23,label:"固定费用",prop:"transaction_same_member_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.transaction_same_member_kickback_fixed,"onUpdate:modelValue":a[64]||(a[64]=e=>ol.transaction_same_member_kickback_fixed=e),min:0,precision:2,onChange:hl},null,8,["modelValue"]),qi])),_:1})):U("",!0),V(Fl,{"content-position":"left"},{default:j((()=>[w("同库房不同店铺")])),_:1}),V(c,{label:"扣库房百分比",prop:"different_shop_warehouse_percent"},{default:j((()=>[V(Ol,{modelValue:ol.different_shop_warehouse_percent,"onUpdate:modelValue":a[65]||(a[65]=e=>ol.different_shop_warehouse_percent=e),min:0,max:100,precision:2,placeholder:"请输入百分比"},null,8,["modelValue"]),Ii])),_:1}),V(c,{label:"扣库房固定金额",prop:"different_shop_warehouse_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.different_shop_warehouse_fixed,"onUpdate:modelValue":a[66]||(a[66]=e=>ol.different_shop_warehouse_fixed=e),min:0,precision:2,placeholder:"请输入固定金额"},null,8,["modelValue"]),Pi])),_:1}),V(c,{label:"扣店主百分比",prop:"different_shop_owner_percent"},{default:j((()=>[V(Ol,{modelValue:ol.different_shop_owner_percent,"onUpdate:modelValue":a[67]||(a[67]=e=>ol.different_shop_owner_percent=e),min:0,max:100,precision:2,placeholder:"请输入百分比"},null,8,["modelValue"]),Ei])),_:1}),V(c,{label:"扣店主固定金额",prop:"different_shop_owner_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.different_shop_owner_fixed,"onUpdate:modelValue":a[68]||(a[68]=e=>ol.different_shop_owner_fixed=e),min:0,precision:2,placeholder:"请输入固定金额"},null,8,["modelValue"]),Ri])),_:1}),V(c,{label:"成本",prop:"different_shop_cost"},{default:j((()=>[V(Ol,{modelValue:ol.different_shop_cost,"onUpdate:modelValue":a[69]||(a[69]=e=>ol.different_shop_cost=e),min:0,precision:2,placeholder:"请输入成本"},null,8,["modelValue"]),Bi])),_:1}),V(c,{label:"佣金类型",prop:"different_shop_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.different_shop_kickback_type,"onUpdate:modelValue":a[70]||(a[70]=e=>ol.different_shop_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.different_shop_kickback_type)?(v(),C(c,{key:24,label:"提点比例",prop:"different_shop_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.different_shop_kickback_point,"onUpdate:modelValue":a[71]||(a[71]=e=>ol.different_shop_kickback_point=e),min:0,max:100,precision:2,onChange:gl},null,8,["modelValue"]),Ki])),_:1})):U("",!0),[2,3].includes(ol.different_shop_kickback_type)?(v(),C(c,{key:25,label:"固定费用",prop:"different_shop_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.different_shop_kickback_fixed,"onUpdate:modelValue":a[72]||(a[72]=e=>ol.different_shop_kickback_fixed=e),min:0,precision:2,onChange:gl},null,8,["modelValue"]),$i])),_:1})):U("",!0),V(c,{label:"会员佣金类型",prop:"different_shop_member_kickback_type"},{default:j((()=>[V(Jl,{modelValue:ol.different_shop_member_kickback_type,"onUpdate:modelValue":a[73]||(a[73]=e=>ol.different_shop_member_kickback_type=e),placeholder:"请选择佣金类型"},{default:j((()=>[V(Nl,{label:"预留",value:0}),V(Nl,{label:"提点",value:1}),V(Nl,{label:"固定费用",value:2}),V(Nl,{label:"提点/固费",value:3})])),_:1},8,["modelValue"])])),_:1}),[1,3].includes(ol.different_shop_member_kickback_type)?(v(),C(c,{key:26,label:"提点比例",prop:"different_shop_member_kickback_point"},{default:j((()=>[V(Ol,{modelValue:ol.different_shop_member_kickback_point,"onUpdate:modelValue":a[74]||(a[74]=e=>ol.different_shop_member_kickback_point=e),min:0,max:100,precision:2,onChange:Vl},null,8,["modelValue"]),Di])),_:1})):U("",!0),[2,3].includes(ol.different_shop_member_kickback_type)?(v(),C(c,{key:27,label:"固定费用",prop:"different_shop_member_kickback_fixed"},{default:j((()=>[V(Ol,{modelValue:ol.different_shop_member_kickback_fixed,"onUpdate:modelValue":a[75]||(a[75]=e=>ol.different_shop_member_kickback_fixed=e),min:0,precision:2,onChange:Vl},null,8,["modelValue"]),Li])),_:1})):U("",!0),V(Fl,{"content-position":"left"},{default:j((()=>[w("基本信息")])),_:1}),V(c,{label:"价格(元)",prop:"price"},{default:j((()=>[V(Ol,{modelValue:ol.price,"onUpdate:modelValue":a[76]||(a[76]=e=>ol.price=e),min:0,precision:0},null,8,["modelValue"])])),_:1}),V(c,{label:"状态",prop:"state"},{default:j((()=>[V(Al,{modelValue:ol.state,"onUpdate:modelValue":a[77]||(a[77]=e=>ol.state=e),"active-value":1,"inactive-value":0,"active-text":"正常","inactive-text":"失效"},null,8,["modelValue"])])),_:1}),V(c,{label:"备注",prop:"note"},{default:j((()=>[V(n,{modelValue:ol.note,"onUpdate:modelValue":a[78]||(a[78]=e=>ol.note=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}});Gi.__scopeId="data-v-5e7cf10f";export{Gi as default};
